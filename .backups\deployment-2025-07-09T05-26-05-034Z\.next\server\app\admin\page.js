(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6958:(e,s,t)=>{Promise.resolve().then(t.bind(t,8882))},8882:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\app\\\\admin\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\page.jsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33888:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(65239),l=t(48088),a=t(88170),i=t.n(a),n=t(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8882)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(t.bind(t,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\page.jsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},36592:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(60687),l=t(43210),a=t(16189);function i(){let[e,s]=(0,l.useState)(!1),[t,i]=(0,l.useState)(""),[n,d]=(0,l.useState)(""),[o,c]=(0,l.useState)(!0),m=(0,a.useRouter)(),x=async e=>{e.preventDefault(),d("");try{let e=await fetch("/api/admin/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:t})}),r=await e.json();e.ok?(localStorage.setItem("admin-token",r.token),s(!0)):d(r.error||"Nieprawidłowe hasło")}catch(e){d("Błąd połączenia")}};return o?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-temple",children:"Ładowanie..."})]})}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-rice to-mist",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-temple/10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("h1",{className:"text-xl font-serif text-temple",children:"Panel Administracyjny"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"text-sm text-wood-light",children:"Zalogowany jako Administrator"}),(0,r.jsx)("button",{onClick:()=>{localStorage.removeItem("admin-token"),s(!1),i("")},className:"text-sm text-temple hover:text-temple/70 transition-colors",children:"Wyloguj się"})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-temple/10 rounded-lg flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCDD"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Sanity CMS"}),(0,r.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj treścią"})]})]}),(0,r.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Edytuj retreaty, opinie, FAQ i artykuły bloga"}),(0,r.jsxs)("a",{href:"https://bakasana-travel.sanity.studio",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-temple text-white text-sm rounded-lg hover:bg-temple/90 transition-colors",children:["Otw\xf3rz CMS",(0,r.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-golden/10 rounded-lg flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCCA"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Analytics"}),(0,r.jsx)("p",{className:"text-sm text-wood-light",children:"Statystyki strony"})]})]}),(0,r.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Zobacz raporty odwiedzin i konwersji"}),(0,r.jsxs)("a",{href:"https://analytics.google.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-golden text-white text-sm rounded-lg hover:bg-golden/90 transition-colors",children:["Google Analytics",(0,r.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-sage/10 rounded-lg flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCC5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Rezerwacje"}),(0,r.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj bookingami"})]})]}),(0,r.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Przeglądaj i zarządzaj rezerwacjami"}),(0,r.jsxs)("button",{onClick:()=>m.push("/admin/bookings"),className:"inline-flex items-center px-4 py-2 bg-sage text-white text-sm rounded-lg hover:bg-sage/90 transition-colors",children:["Zobacz rezerwacje",(0,r.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-lotus/10 rounded-lg flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCE7"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Newsletter"}),(0,r.jsx)("p",{className:"text-sm text-wood-light",children:"Zarządzaj subskrypcjami"})]})]}),(0,r.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Przeglądaj subskrybent\xf3w i wysyłaj kampanie"}),(0,r.jsxs)("button",{onClick:()=>m.push("/admin/newsletter"),className:"inline-flex items-center px-4 py-2 bg-lotus text-white text-sm rounded-lg hover:bg-lotus/90 transition-colors",children:["Zarządzaj newsletter",(0,r.jsx)("span",{className:"ml-2",children:"→"})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-wood/10 rounded-lg flex items-center justify-center mr-4",children:(0,r.jsx)("span",{className:"text-2xl",children:"⚙️"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple",children:"Ustawienia"}),(0,r.jsx)("p",{className:"text-sm text-wood-light",children:"Konfiguracja strony"})]})]}),(0,r.jsx)("p",{className:"text-wood-light text-sm mb-4",children:"Zarządzaj ustawieniami i konfiguracją"}),(0,r.jsxs)("button",{onClick:()=>m.push("/admin/settings"),className:"inline-flex items-center px-4 py-2 bg-wood text-white text-sm rounded-lg hover:bg-wood/90 transition-colors",children:["Ustawienia",(0,r.jsx)("span",{className:"ml-2",children:"→"})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white rounded-xl shadow-soft p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple mb-4",children:"Szybkie statystyki"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-temple/5 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:"Rezerwacje"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-golden/5 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:"Newsletter"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-sage/5 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:"Odwiedziny"})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-lotus/5 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-temple",children:"-"}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:"Konwersje"})]})]})]})]})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-rice to-mist",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-soft max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h1",{className:"text-2xl font-serif text-temple mb-2",children:"Panel Administracyjny"}),(0,r.jsx)("p",{className:"text-wood-light",children:"Bakasana Travel Blog"})]}),(0,r.jsxs)("form",{onSubmit:x,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-temple mb-2",children:"Hasło administratora"}),(0,r.jsx)("input",{type:"password",id:"password",value:t,onChange:e=>i(e.target.value),className:"w-full px-4 py-3 border border-temple/20 rounded-lg focus:ring-2 focus:ring-temple/20 focus:border-temple transition-colors",placeholder:"Wprowadź hasło",required:!0})]}),n&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm",children:n}),(0,r.jsx)("button",{type:"submit",className:"w-full bg-temple text-white py-3 px-4 rounded-lg hover:bg-temple/90 transition-colors font-medium",children:"Zaloguj się"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsx)("p",{className:"text-xs text-wood-light",children:"\uD83D\uDD12 Zabezpieczone połączenie"})})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70006:(e,s,t)=>{Promise.resolve().then(t.bind(t,36592))}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>t(33888));module.exports=r})();