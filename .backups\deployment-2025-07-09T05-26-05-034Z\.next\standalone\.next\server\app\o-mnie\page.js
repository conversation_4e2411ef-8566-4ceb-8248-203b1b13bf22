"use strict";(()=>{var e={};e.id=1046,e.ids=[1046],e.modules={2556:(e,a,i)=>{i.r(a),i.d(a,{default:()=>n,metadata:()=>s});var t=i(37413);i(61120);var r=i(53384),o=i(75124);let s=(0,o.X2)({title:"<PERSON> - Instruktorka Jogi i Fizjoterapeutka",description:"Poznaj Julię <PERSON>icz - magistra fizjoterapii z 8-letnim doświadczeniem i certyfikowaną instruktorkę jogi RYT 500. Organizatorka retreat\xf3w jogowych na Bali od 2020 roku.",keywords:["<PERSON> J<PERSON>","instruktorka jogi RYT 500","fizjoterapeutka magister","joga terapeutyczna","organizator retreat\xf3w Bali","Yoga Alliance","doświadczenie fizjoterapia","holistyczne podejście joga"]});function n(){let e=(0,o.F8)({type:"Person",name:"<PERSON>",description:"Magister fizjoterapii i certyfikowana instruktorka jogi RYT 500. Organizatorka retreat\xf3w jogowych na Bali."});return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}}),(0,t.jsxs)("main",{className:"relative min-h-screen bg-secondary",children:[(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-text container-padding",children:(0,t.jsxs)("div",{className:"section-title",children:[(0,t.jsx)("h1",{className:"text-3xl md:text-4xl font-serif text-temple mb-8 font-light",children:"Julia Jakubowicz"}),(0,t.jsx)("p",{className:"text-lg text-wood-light font-light max-w-2xl mx-auto",children:"Poznaj moją drogę od fizjoterapii do instruktorki jogi i organizatorki transformacyjnych retreat\xf3w na Bali"}),(0,t.jsx)("div",{className:"decorative-line"})]})})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-text container-padding",children:(0,t.jsxs)("div",{className:"card p-8 md:p-12",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-serif text-temple mb-6 font-light",children:"Pasja do holistycznego zdrowia"}),(0,t.jsxs)("div",{className:"w-24 h-24 mx-auto mb-8 relative rounded-full overflow-hidden",children:[(0,t.jsx)(r.default,{src:"/images/profile/omnie-opt.webp",alt:"Julia Jakubowicz - Instruktorka jogi i fizjoterapeutka",fill:!0,priority:!0,className:"object-cover",sizes:"96px"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-temple/10"})]})]}),(0,t.jsxs)("div",{className:"space-y-6 text-primary font-light",children:[(0,t.jsx)("p",{children:"Jestem magistrem fizjoterapii z 8-letnim doświadczeniem klinicznym oraz certyfikowaną instruktorką jogi (RYT 500). Moją pasją jest łączenie wiedzy medycznej z duchową praktyką jogi, tworząc holistyczne podejście do zdrowia i dobrostanu."}),(0,t.jsx)("p",{children:"Organizuję retreaty jogowe na Bali, gdzie dzielę się nie tylko technikami jogi, ale także magią tej wyjątkowej wyspy. Moje programy łączą tradycyjną praktykę z nowoczesną wiedzą o anatomii i biomechanice."}),(0,t.jsxs)("p",{children:["W Polsce prowadzę ",(0,t.jsx)("a",{href:"https://flywithbakasana.pl/",target:"_blank",rel:"noopener noreferrer",className:"text-temple hover:text-golden transition-colors",children:"Studio Bakasana w Rzeszowie"}),", gdzie regularnie uczę r\xf3żnych styl\xf3w jogi: Hatha, Vinyasa, Ashtanga Flow, jogi dla kobiet w ciąży oraz senior\xf3w."]}),(0,t.jsx)("p",{children:"Specjalizuję się w jodze terapeutycznej, pomagając osobom z problemami kręgosłupa, b\xf3lami plec\xf3w i zaburzeniami postawy. Wierzę, że każdy może praktykować jogę - niezależnie od wieku, kondycji czy doświadczenia."})]})]})})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsxs)("div",{className:"max-width-text container-padding",children:[(0,t.jsxs)("div",{className:"section-title",children:[(0,t.jsx)("h3",{className:"text-xl font-serif text-temple mb-6 font-light",children:"Kwalifikacje i Doświadczenie"}),(0,t.jsx)("p",{className:"text-primary/70 mb-8 font-light",children:"Profesjonalne wykształcenie i certyfikaty"}),(0,t.jsx)("div",{className:"decorative-line"})]}),(0,t.jsx)("div",{className:"card p-6",children:(0,t.jsx)("div",{className:"space-y-4",children:["Magister fizjoterapii z 8-letnim doświadczeniem klinicznym","Certyfikowana instruktorka jogi (RYT 500) - Yoga Alliance","Specjalizacja w jodze terapeutycznej i rehabilitacyjnej","Ukończone kursy: Vinyasa, Hatha, Yin Yoga, Pranayama","Organizator retreat\xf3w jogowych na Bali od 2020 roku","Autorka program\xf3w łączących fizjoterapię z praktyką jogi","Wsp\xf3łpraca z ośrodkami rehabilitacyjnymi i studiami jogi","Certyfikat w zakresie anatomii i biomechaniki ruchu"].map((e,a)=>(0,t.jsxs)("div",{className:"flex items-start gap-3 py-3 border-b border-temple/5 last:border-b-0",children:[(0,t.jsx)("div",{className:"w-1 h-1 bg-temple/30 mt-3 flex-shrink-0 rounded-full"}),(0,t.jsx)("span",{className:"text-primary/80 font-light",children:e})]},a))})})]})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsxs)("div",{className:"max-width-text container-padding",children:[(0,t.jsxs)("div",{className:"section-title",children:[(0,t.jsx)("h3",{className:"text-xl font-serif text-temple mb-6 font-light",children:"Profesjonalne Osiągnięcia"}),(0,t.jsx)("p",{className:"text-primary/70 mb-8 font-light",children:"Moje najważniejsze sukcesy zawodowe"}),(0,t.jsx)("div",{className:"decorative-line"})]}),(0,t.jsx)("div",{className:"space-y-6",children:[{title:"Ponad 2000 godzin nauczania jogi",description:"Doświadczenie w pracy z r\xf3żnymi grupami wiekowymi i poziomami zaawansowania",category:"Doświadczenie"},{title:"Kilka udanych retreat\xf3w na Bali",description:"Zorganizowane transformacyjne wyjazdy dla uczestnik\xf3w z całej Polski",category:"Retreaty"},{title:"Autorska metodyka łączenia fizjoterapii z jogą",description:"Innowacyjne podejście do terapii b\xf3lu plec\xf3w i problem\xf3w postawy",category:"Specjalizacja"},{title:"Wsp\xf3łpraca z ekspertami",description:"Stała wsp\xf3łpraca z lekarzami ortopedami i fizjoterapeutami",category:"Partnerstwa"}].map((e,a)=>(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("h4",{className:"text-lg font-serif text-temple mb-3 font-light",children:e.title}),(0,t.jsx)("p",{className:"text-primary/80 font-light",children:e.description})]},a))})]})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsxs)("div",{className:"max-width-text container-padding",children:[(0,t.jsxs)("div",{className:"section-title",children:[(0,t.jsx)("h3",{className:"text-xl font-serif text-temple mb-6 font-light",children:"Filozofia Pracy"}),(0,t.jsx)("p",{className:"text-primary/70 mb-8 font-light",children:"Wartości kt\xf3re kierują moją praktyką"}),(0,t.jsx)("div",{className:"decorative-line"})]}),(0,t.jsx)("div",{className:"space-y-6",children:[{title:"Holistyczne podejście",description:"Łączę wiedzę medyczną z duchową praktyką jogi, tworząc kompleksowe programy zdrowotne.",category:"Metodyka"},{title:"Indywidualizacja",description:"Każdy uczestnik otrzymuje personalne wskaz\xf3wki dostosowane do jego potrzeb i możliwości.",category:"Podejście"},{title:"Bezpieczeństwo przede wszystkim",description:"Moje doświadczenie fizjoterapeutyczne gwarantuje bezpieczną praktykę dla wszystkich.",category:"Priorytet"},{title:"Transformacja przez podr\xf3ż",description:"Wierzę, że połączenie jogi z magią Bali tworzy przestrzeń dla głębokiej przemiany.",category:"Wizja"}].map((e,a)=>(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("h4",{className:"text-lg font-serif text-temple mb-3 font-light",children:e.title}),(0,t.jsx)("p",{className:"text-primary/80 font-light",children:e.description})]},a))})]})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-text container-padding",children:(0,t.jsxs)("div",{className:"glass-effect rounded-2xl p-8 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-serif text-temple mb-6 font-light",children:"Gotowa na transformację?"}),(0,t.jsx)("p",{className:"text-primary/70 mb-8 font-light max-w-lg mx-auto",children:"Dołącz do mnie na Bali i odkryj, jak joga może zmienić Twoje życie. Każdy retreat to unikalna podr\xf3ż łącząca praktykę z magią wyspy."}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 justify-center items-center",children:[(0,t.jsx)("a",{href:"/program",className:"btn-soft-golden","aria-label":"Zobacz programy retreat\xf3w jogowych na Bali",children:"Zobacz Programy Retreat\xf3w"}),(0,t.jsx)("span",{className:"text-temple/30 text-sm",children:"lub"}),(0,t.jsx)("a",{href:"/kontakt",className:"btn-soft","aria-label":"Skontaktuj się z Julią",children:"Skontaktuj się ze mną"})]})]})})})]})]})}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},51436:(e,a,i)=>{i.r(a),i.d(a,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=i(65239),r=i(48088),o=i(88170),s=i.n(o),n=i(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);i.d(a,l);let c={children:["",{children:["o-mnie",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,2556)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\o-mnie\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(i.bind(i,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(i.bind(i,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\o-mnie\\page.jsx"],p={require:i,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/o-mnie/page",pathname:"/o-mnie",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var a=require("../../webpack-runtime.js");a.C(e);var i=e=>a(a.s=e),t=a.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>i(51436));module.exports=t})();