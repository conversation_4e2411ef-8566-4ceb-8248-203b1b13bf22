"use strict";(()=>{var e={};e.id=2129,e.ids=[2129],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56066:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>j,serverHooks:()=>y,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{GET:()=>x,POST:()=>g});var s=r(96559),o=r(48088),a=r(37719),i=r(32190),p=r(80829),u=r.n(p);require("crypto");let d=process.env.ADMIN_PASSWORD||"BakasanaAdmin2024!",l=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production",c=new Map;function m(e){let t=c.get(e)||{count:0,lastAttempt:0};t.count+=1,t.lastAttempt=Date.now(),c.set(e,t)}async function g(e){try{let t=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);if(function(e){let t=c.get(e);return!!t&&(Date.now()-t.lastAttempt>9e5?(c.delete(e),!1):t.count>=5)}(t))return i.NextResponse.json({success:!1,error:"Zbyt wiele nieudanych pr\xf3b logowania. Spr\xf3buj ponownie za 15 minut.",lockoutTime:15},{status:429});let{password:r}=await e.json();if(!r)return m(t),i.NextResponse.json({success:!1,error:"Hasło jest wymagane"},{status:400});if(!await w(r,d))return m(t),console.warn(`Failed admin login attempt from IP: ${t} at ${new Date().toISOString()}`),i.NextResponse.json({success:!1,error:"Nieprawidłowe hasło"},{status:401});c.delete(t);let n=u().sign({role:"admin",ip:t,loginTime:Date.now()},l,{expiresIn:"24h",issuer:"bakasana-travel-admin",audience:"bakasana-travel-app"});return i.NextResponse.json({success:!0,token:n,expiresIn:86400,message:"Zalogowano pomyślnie"})}catch(e){return console.error("Admin login error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas logowania",details:void 0},{status:500})}}async function w(e,t){return e===t}async function x(){return i.NextResponse.json({message:"Admin login endpoint is working",timestamp:new Date().toISOString(),rateLimit:{maxAttempts:5,lockoutTimeMinutes:15}})}let j=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/login/route",pathname:"/api/admin/login",filename:"route",bundlePath:"app/api/admin/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\api\\admin\\login\\route.js",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:k,workUnitAsyncStorage:v,serverHooks:y}=j;function f(){return(0,a.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:v})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80829:e=>{e.exports=require("jsonwebtoken")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>r(56066));module.exports=n})();