'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

const PaymentMethods = ({ onPaymentMethodChange, selectedMethod }) => {
  const paymentMethods = [
    {
      id: 'blik',
      name: 'BLI<PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> przez aplikację bankową',
      icon: '📱',
      popular: true,
      processingTime: 'Natychmiastowa',
      features: ['<PERSON><PERSON><PERSON><PERSON><PERSON> płat<PERSON>', 'Natychmiastowe potwierdzenie', '<PERSON><PERSON> prowizji']
    },
    {
      id: 'transfer',
      name: 'Przelew bankowy',
      description: 'Tradycyjny przelew na konto',
      icon: '🏦',
      popular: false,
      processingTime: '1-2 dni robocze',
      features: ['<PERSON><PERSON> prowizji', '<PERSON><PERSON><PERSON><PERSON><PERSON> płat<PERSON>', 'Sprawdzona metoda']
    },
    {
      id: 'fitssey',
      name: '<PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON><PERSON><PERSON>ś<PERSON> przez platform<PERSON>',
      icon: '💳',
      popular: false,
      processingTime: '<PERSON>ychmiasto<PERSON>',
      features: ['<PERSON><PERSON><PERSON> pła<PERSON>', '<PERSON><PERSON><PERSON><PERSON> w aplikacji', 'Raty 0%']
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-serif text-temple mb-4">Wybierz metodę płatności zadatku</h3>
      
      <div className="grid gap-4">
        {paymentMethods.map((method) => (
          <motion.div
            key={method.id}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`
              relative border p-4 cursor-pointer transition-all duration-300
              ${selectedMethod === method.id
                ? 'border-accent bg-accent/5 shadow-soft'
                : 'border-accent/20 hover:border-accent/40 hover:bg-accent/2'
              }
            `}
            onClick={() => onPaymentMethodChange(method.id)}
          >
            {method.popular && (
              <div className="absolute -top-2 left-4 bg-accent/90 text-primary text-xs px-3 py-1 border border-accent/20">
                Popularne
              </div>
            )}
            
            <div className="flex items-start gap-4">
              <div className="text-2xl">{method.icon}</div>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-temple">{method.name}</h4>
                  <span className="text-sm text-wood-light">• {method.processingTime}</span>
                </div>
                <p className="text-sm text-wood-light mb-3">{method.description}</p>
                
                <div className="flex flex-wrap gap-2">
                  {method.features.map((feature, index) => (
                    <span 
                      key={index}
                      className="text-xs bg-temple/10 text-temple px-2 py-1 rounded-full"
                    >
                      ✓ {feature}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className={`
                w-6 h-6 rounded-full border-2 flex items-center justify-center
                ${selectedMethod === method.id 
                  ? 'border-temple bg-temple' 
                  : 'border-gray-300'
                }
              `}>
                {selectedMethod === method.id && (
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                )}
              </div>
            </div>
            
            {/* Payment method specific info */}
            {selectedMethod === method.id && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 pt-4 border-t border-temple/20"
              >
                {method.id === 'blik' && (
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">Jak płacić BLIK-iem:</h5>
                    <ol className="text-sm text-blue-800 space-y-1">
                      <li>1. Po wysłaniu formularza otrzymasz link do płatności</li>
                      <li>2. Kliknij link i wpisz kod BLIK z aplikacji bankowej</li>
                      <li>3. Potwierdź płatność w aplikacji</li>
                      <li>4. Otrzymasz natychmiastowe potwierdzenie</li>
                    </ol>
                  </div>
                )}
                
                {method.id === 'transfer' && (
                  <div className="bg-green-50 p-3 rounded-lg">
                    <h5 className="font-medium text-green-900 mb-2">Dane do przelewu:</h5>
                    <div className="text-sm text-green-800 space-y-1">
                      <div>Odbiorca: Julia Jakubowicz</div>
                      <div>Bank: PKO Bank Polski</div>
                      <div>Tytuł: Zadatek retreat + numer rezerwacji</div>
                      <div className="text-xs mt-2 opacity-80">
                        *Pełne dane otrzymasz po wysłaniu formularza
                      </div>
                    </div>
                  </div>
                )}
                
                {method.id === 'fitssey' && (
                  <div className="bg-purple-50 p-3 rounded-lg">
                    <h5 className="font-medium text-purple-900 mb-2">Płatność przez Fitssey:</h5>
                    <div className="text-sm text-purple-800 space-y-1">
                      <div>• Karty Visa, Mastercard, maestro</div>
                      <div>• BLIK bezpośrednio w aplikacji</div>
                      <div>• Możliwość płatności w ratach 0%</div>
                      <div>• Link otrzymasz na email</div>
                    </div>
                  </div>
                )}
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>
      
      {/* Security badges */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-center gap-4 text-sm text-wood-light">
          <div className="flex items-center gap-1">
            <span>🔒</span>
            <span>Szyfrowanie SSL</span>
          </div>
          <div className="flex items-center gap-1">
            <span>🛡️</span>
            <span>Bezpieczne płatności</span>
          </div>
          <div className="flex items-center gap-1">
            <span>✅</span>
            <span>Weryfikowane</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethods;