"use strict";exports.id=3434,exports.ids=[3434],exports.modules={4980:(t,e,n)=>{n.d(e,{f:()=>h});var i=n(48219),s=n(2683),o=n(87153),r=n(64496),a=n(10080),l=n(12099);let u=t=>(e,n)=>{t&&i.Gt.postRender(()=>t(e,n))};class h extends r.X{constructor(){super(...arguments),this.removePointerDownListener=s.lQ}onPointerDown(t){this.session=new l.Q(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:(0,a.s)(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:s}=this.node.getProps();return{onSessionStart:u(t),onStart:u(e),onMove:n,onEnd:(t,e)=>{delete this.session,s&&i.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=(0,o.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},12099:(t,e,n)=>{n.d(e,{Q:()=>l});var i=n(48219),s=n(2683),o=n(87153),r=n(27100),a=n(73426);class l{constructor(t,e,{transformPagePoint:n,contextWindow:l,dragSnapToOrigin:h=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=void 0,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=c(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=(0,a.w)(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;let{point:s}=t,{timestamp:o}=i.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:l}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=u(e,this.transformPagePoint),i.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=c("pointercancel"===t.type?this.lastMoveEventInfo:u(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!(0,i.Mc)(t))return;this.dragSnapToOrigin=h,this.handlers=e,this.transformPagePoint=n,this.contextWindow=l||void 0;let d=u((0,r.e)(t),this.transformPagePoint),{point:m}=d,{timestamp:p}=i.uv;this.history=[{...m,timestamp:p}];let{onSessionStart:f}=e;f&&f(t,c(d,this.history)),this.removeListeners=(0,s.Fs)((0,o.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,o.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,o.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,i.WG)(this.updatePoint)}}function u(t,e){return e?{point:e(t.point)}:t}function h(t,e){return{x:t.x-e.x,y:t.y-e.y}}function c({point:t},e){return{point:t,delta:h(t,d(e)),offset:h(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null,o=d(t);for(;n>=0&&(i=t[n],!(o.timestamp-i.timestamp>(0,s.fD)(.1)));)n--;if(!i)return{x:0,y:0};let r=(0,s.Xu)(o.timestamp-i.timestamp);if(0===r)return{x:0,y:0};let a={x:(o.x-i.x)/r,y:(o.y-i.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function d(t){return t[t.length-1]}},12157:(t,e,n)=>{n.d(e,{L:()=>i});let i=(0,n(43210).createContext)({})},13500:(t,e,n)=>{n.d(e,{c:()=>r});var i=n(2683),s=n(58902),o=n(64496);class r extends o.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,i.Fs)((0,s.k)(this.node.current,"focus",()=>this.onFocus()),(0,s.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},21279:(t,e,n)=>{n.d(e,{t:()=>i});let i=(0,n(43210).createContext)(null)},23007:(t,e,n)=>{n.d(e,{_:()=>d});var i=n(44693),s=n(48219),o=n(54642),r=n(67283),a=n(80722),l=n(47577);function u(t,e,{delay:n=0,transitionOverride:i,type:h}={}){let{transition:c=t.getDefaultTransition(),transitionEnd:d,...m}=e;i&&(c=i);let p=[],f=h&&t.animationState&&t.animationState.getState()[h];for(let e in m){let i=t.getValue(e,t.latestValues[e]??null),o=m[e];if(void 0===o||f&&function({protectedKeys:t,needsAnimating:e},n){let i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}(f,e))continue;let u={delay:n,...(0,s.rU)(c||{},e)},h=i.get();if(void 0!==h&&!i.isAnimating&&!Array.isArray(o)&&o===h&&!u.velocity)continue;let d=!1;if((void 0).MotionHandoffAnimation){let n=(0,a.P)(t);if(n){let t=(void 0).MotionHandoffAnimation(n,e,s.Gt);null!==t&&(u.startTime=t,d=!0)}}(0,r.g)(t,e),i.start((0,l.f)(e,i,o,t.shouldReduceMotion&&s.$y.has(e)?{type:!1}:u,t,d));let v=i.animation;v&&p.push(v)}return d&&Promise.all(p).then(()=>{s.Gt.update(()=>{d&&(0,o.U)(t,d)})}),p}function h(t,e,n={}){let s=(0,i.K)(t,e,"exit"===n.type?t.presenceContext?.custom:void 0),{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);let r=s?()=>Promise.all(u(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){let r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(c).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(h(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(!l)return Promise.all([r(),a(n.delay)]);{let[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}}function c(t,e){return t.sortNodePosition(e)}function d(t,e,n={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>h(t,e,n)));else if("string"==typeof e)s=h(t,e,n);else{let o="function"==typeof e?(0,i.K)(t,e,n.custom):e;s=Promise.all(u(t,o,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}},27100:(t,e,n)=>{n.d(e,{F:()=>o,e:()=>s});var i=n(48219);function s(t){return{point:{x:t.pageX,y:t.pageY}}}let o=t=>e=>(0,i.Mc)(e)&&t(e,s(e))},27292:(t,e,n)=>{n.d(e,{p:()=>i});let i=t=>Array.isArray(t)},30765:(t,e,n)=>{n.d(e,{H:()=>a});var i=n(48219),s=n(27100),o=n(64496);function r(t,e,n){let{props:o}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&o.whileTap&&t.animationState.setActive("whileTap","Start"===n);let r=o["onTap"+("End"===n?"":n)];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends o.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.c$)(t,(t,e)=>(r(this.node,e,"Start"),(t,{success:e})=>r(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},32582:(t,e,n)=>{n.d(e,{Q:()=>i});let i=(0,n(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},34213:(t,e,n)=>{n.d(e,{A:()=>i});let i=(0,n(43210).createContext)({})},43436:(t,e,n)=>{n.d(e,{Y:()=>i});let i=(0,n(43210).createContext)({strict:!1})},47577:(t,e,n)=>{n.d(e,{f:()=>c});var i=n(48219),s=n(2683);let o=t=>null!==t,r={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),l={type:"keyframes",duration:.8},u={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},h=(t,{keyframes:e})=>e.length>2?l:i.fu.has(t)?t.startsWith("scale")?a(e[1]):r:u,c=(t,e,n,r={},a,l)=>u=>{let c=(0,i.rU)(r,t)||{},d=c.delay||r.delay||0,{elapsed:m=0}=r;m-=(0,s.fD)(d);let p={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...c,delay:-m,onUpdate:t=>{e.set(t),c.onUpdate&&c.onUpdate(t)},onComplete:()=>{u(),c.onComplete&&c.onComplete()},name:t,motionValue:e,element:l?void 0:a};!function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(c)&&Object.assign(p,h(t,p)),p.duration&&(p.duration=(0,s.fD)(p.duration)),p.repeatDelay&&(p.repeatDelay=(0,s.fD)(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let f=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(f=!0)),(s.W9.instantAnimations||s.W9.skipAnimations)&&(f=!0,p.duration=0,p.delay=0),p.allowFlatten=!c.type&&!c.ease,f&&!l&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:n="loop"},i){let s=t.filter(o),r=e&&"loop"!==n&&e%2==1?0:s.length-1;return s[r]}(p.keyframes,c);if(void 0!==t)return void i.Gt.update(()=>{p.onUpdate(t),p.onComplete()})}return c.isSync?new i.sb(p):new i.AT(p)}},51756:(t,e,n)=>{n.d(e,{n:()=>i});let i="data-"+(0,n(67886).I)("framerAppearId")},56570:(t,e,n)=>{n.d(e,{N:()=>i});function i(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}},58902:(t,e,n)=>{n.d(e,{k:()=>i});function i(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}},65820:(t,e,n)=>{n.d(e,{w:()=>D});var i=n(64496),s=n(2683),o=n(48219),r=n(47577),a=n(58902),l=n(87153),u=n(27100),h=n(32572),c=n(20172),d=n(54538),m=n(27642),p=n(92953),f=n(10080),v=n(39853),g=n(67283),y=n(12099);function x(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function P(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function E(t,e,n){return{min:A(t,e),max:A(t,n)}}function A(t,e){return"number"==typeof t?t:t[e]||0}let M=new WeakMap;class C{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,d.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new y.Q(t,{onSessionStart:t=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,u.e)(t).point)},onStart:(t,e)=>{let{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=(0,o.Wp)(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,m.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(o.rq.test(e)){let{projection:n}=this.visualElement;if(n&&n.layout){let i=n.layout.layoutBox[t];i&&(e=(0,c.CQ)(i)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&o.Gt.postRender(()=>s(t,e)),(0,g.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:r}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}(r),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,m.X)(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:(0,f.s)(this.visualElement)})}stop(t,e){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:s}=this.getProps();s&&o.Gt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){let{drag:i}=this.getProps();if(!n||!S(t,i,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?(0,o.k$)(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?(0,o.k$)(n,t,i.max):Math.min(t,n)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&(0,v.X)(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(t,{top:e,left:n,bottom:i,right:s}){return{x:x(t.x,n,s),y:x(t.y,e,i)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:E(t,"left","right"),y:E(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&(0,m.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!(0,v.X)(e))return!1;let i=e.current;(0,s.V1)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let r=(0,p.L)(i,o.root,this.visualElement.getTransformPagePoint()),a=(t=o.layout.layoutBox,{x:P(t.x,r.x),y:P(t.y,r.y)});if(n){let t=n((0,h.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,h.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{};return Promise.all((0,m.X)(r=>{if(!S(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:n?t[r]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,u)})).then(r)}startAxisValueAnimation(t,e){let n=this.getAxisMotionValue(t);return(0,g.g)(this.visualElement,t),n.start((0,r.f)(t,n,0,e,this.visualElement,!1))}stopAnimation(){(0,m.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,m.X)(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps();return n[e]||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){(0,m.X)(e=>{let{drag:n}=this.getProps();if(!S(e,n,this.currentDirection))return;let{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){let{min:n,max:r}=i.layout.layoutBox[e];s.set(t[e]-(0,o.k$)(n,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!(0,v.X)(e)||!n||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};(0,m.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let n=e.get();i[t]=function(t,e){let n=.5,i=(0,c.CQ)(t),o=(0,c.CQ)(e);return o>i?n=(0,s.qB)(e.min,e.max-i,t.min):i>o&&(n=(0,s.qB)(t.min,t.max-o,e.min)),(0,s.qE)(0,1,n)}({min:n,max:n},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),(0,m.X)(e=>{if(!S(e,t,null))return;let n=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];n.set((0,o.k$)(s,r,i[e]))})}addListeners(){if(!this.visualElement.current)return;M.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),n=()=>{let{dragConstraints:t}=this.getProps();(0,v.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",n);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),o.Gt.read(n);let r=(0,a.k)(void 0,"resize",()=>this.scalePositionWithinConstraints()),u=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,m.X)(e=>{let n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),e(),s(),u&&u()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=.35,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function S(t,e,n){return(!0===e||e===t)&&(null===n||n===t)}class D extends i.X{constructor(t){super(t),this.removeGroupControls=s.lQ,this.removeListeners=s.lQ,this.controls=new C(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||s.lQ}unmount(){this.removeGroupControls(),this.removeListeners()}}},70478:(t,e,n)=>{n.d(e,{e:()=>a});var i=n(48219),s=n(27100),o=n(64496);function r(t,e,n){let{props:o}=t;t.animationState&&o.whileHover&&t.animationState.setActive("whileHover","Start"===n);let r=o["onHover"+n];r&&i.Gt.postRender(()=>r(e,(0,s.e)(e)))}class a extends o.X{mount(){let{current:t}=this.node;t&&(this.unmount=(0,i.PT)(t,(t,e)=>(r(this.node,e,"Start"),t=>r(this.node,t,"End"))))}unmount(){}}},71754:(t,e,n)=>{n.d(e,{z:()=>a});var i=n(43210),s=n(34213),o=n(57529),r=n(90567);function a(t){let{initial:e,animate:n}=function(t,e){if((0,o.e)(t)){let{initial:e,animate:n}=t;return{initial:!1===e||(0,r.w)(e)?e:void 0,animate:(0,r.w)(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(s.A));return(0,i.useMemo)(()=>({initial:e,animate:n}),[l(e),l(n)])}function l(t){return Array.isArray(t)?t.join(" "):t}},75944:(t,e,n)=>{n.d(e,{z:()=>o});var i=n(48219),s=n(47577);function o(t,e,n){let o=(0,i.SS)(t)?t:(0,i.OQ)(t);return o.start((0,s.f)("",o,e,n)),o.animation}},80722:(t,e,n)=>{n.d(e,{P:()=>s});var i=n(51756);function s(t){return t.props[i.n]}},83641:(t,e,n)=>{n.d(e,{N:()=>i});let i=(0,n(43210).createContext)({})},86044:(t,e,n)=>{n.d(e,{xQ:()=>o});var i=n(43210),s=n(21279);function o(t=!0){let e=(0,i.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:n,onExitComplete:r,register:a}=e,l=(0,i.useId)(),u=(0,i.useCallback)(()=>t&&r&&r(l),[l,r,t]);return!n&&r?[!1,u]:[!0]}},87153:(t,e,n)=>{n.d(e,{h:()=>o});var i=n(58902),s=n(27100);function o(t,e,n,o){return(0,i.k)(t,e,(0,s.F)(n),o)}},88920:(t,e,n)=>{n.d(e,{N:()=>y});var i=n(60687),s=n(43210),o=n(12157),r=n(72789),a=n(15124),l=n(21279),u=n(48219),h=n(32582);class c extends s.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,n=(0,u.$P)(t)&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:t,isPresent:e,anchorX:n}){let o=(0,s.useId)(),r=(0,s.useRef)(null),a=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,s.useContext)(h.Q);return(0,s.useInsertionEffect)(()=>{let{width:t,height:i,top:s,left:u,right:h}=a.current;if(e||!r.current||!t||!i)return;let c="left"===n?`left: ${u}`:`right: ${h}`;r.current.dataset.motionPopId=o;let d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${i}px !important;
            ${c}px !important;
            top: ${s}px !important;
          }
        `),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[e]),(0,i.jsx)(c,{isPresent:e,childRef:r,sizeRef:a,children:s.cloneElement(t,{ref:r})})}let m=({children:t,initial:e,isPresent:n,onExitComplete:o,custom:a,presenceAffectsLayout:u,mode:h,anchorX:c})=>{let m=(0,r.M)(p),f=(0,s.useId)(),v=!0,g=(0,s.useMemo)(()=>(v=!1,{id:f,initial:e,isPresent:n,custom:a,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;o&&o()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[n,m,o]);return u&&v&&(g={...g}),(0,s.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[n]),s.useEffect(()=>{n||m.size||!o||o()},[n]),"popLayout"===h&&(t=(0,i.jsx)(d,{isPresent:n,anchorX:c,children:t})),(0,i.jsx)(l.t.Provider,{value:g,children:t})};function p(){return new Map}var f=n(86044);let v=t=>t.key||"";function g(t){let e=[];return s.Children.forEach(t,t=>{(0,s.isValidElement)(t)&&e.push(t)}),e}let y=({children:t,custom:e,initial:n=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:h="sync",propagate:c=!1,anchorX:d="left"})=>{let[p,y]=(0,f.xQ)(c),x=(0,s.useMemo)(()=>g(t),[t]),P=c&&!p?[]:x.map(v),E=(0,s.useRef)(!0),A=(0,s.useRef)(x),M=(0,r.M)(()=>new Map),[C,S]=(0,s.useState)(x),[D,w]=(0,s.useState)(x);(0,a.E)(()=>{E.current=!1,A.current=x;for(let t=0;t<D.length;t++){let e=v(D[t]);P.includes(e)?M.delete(e):!0!==M.get(e)&&M.set(e,!1)}},[D,P.length,P.join("-")]);let L=[];if(x!==C){let t=[...x];for(let e=0;e<D.length;e++){let n=D[e],i=v(n);P.includes(i)||(t.splice(e,0,n),L.push(n))}return"wait"===h&&L.length&&(t=L),w(g(t)),S(x),null}let{forceRender:k}=(0,s.useContext)(o.L);return(0,i.jsx)(i.Fragment,{children:D.map(t=>{let s=v(t),o=(!c||!!p)&&(x===D||P.includes(s));return(0,i.jsx)(m,{isPresent:o,initial:(!E.current||!!n)&&void 0,custom:e,presenceAffectsLayout:u,mode:h,onExitComplete:o?void 0:()=>{if(!M.has(s))return;M.set(s,!0);let t=!0;M.forEach(e=>{e||(t=!1)}),t&&(k?.(),w(A.current),c&&y?.(),l&&l())},anchorX:d,children:t},s)})})}}};