import React from 'react';

// ELEGANCKA NAWIGACJA - SPA HOTEL 5*
const staticNavLinks = [
  {
    href: '/program',
    label: 'retreaty',
    submenu: [
      { href: '/program?destination=bali', label: 'bali - 12 dni' },
      { href: '/program?destination=srilanka', label: 'sri lanka - 10 dni' }
    ]
  },
  { href: '/o-mnie', label: 'o mnie' },
  { href: '/opinie', label: 'opinie' },
  {
    href: '/kontakt',
    label: 'kontakt',
    submenu: [
      { href: '/kontakt', label: 'kontakt' },
      { href: '/zajecia-online', label: 'zajęcia online' }
    ]
  },
];

export default function ServerNavbar() {
  return (
    <>
      <header className="bg-primary/95 backdrop-blur-sm transition-all duration-400 h-20 shadow-soft fixed top-0 left-0 right-0 z-50">
        <div className="max-w-7xl mx-auto px-6 h-full">
          <div className="flex items-center justify-between h-full">
            {/* Logo - Playfair Display, elegancki */}
            <a href="/" className="flex items-center group">
              <span className="text-2xl font-logo font-normal text-dark-gray tracking-[3px] transition-all duration-400 group-hover:text-sage">
                BAKASANA
              </span>
            </a>

            {/* Desktop Navigation - Source Sans Pro 300 */}
            <nav className="hidden lg:flex items-center space-x-10">
              {staticNavLinks.map((link) => (
                <div key={link.href} className="relative group">
                  <a
                    href={link.href}
                    className="text-sm font-light text-text-gray hover:text-dark-gray transition-all duration-400 px-2 py-3 tracking-wide relative group flex items-center gap-1"
                  >
                    {link.label}
                    {link.submenu && (
                      <span className="text-xs transition-transform duration-400 group-hover:rotate-180">▼</span>
                    )}
                    {/* Animowane podkreślenie od lewej do prawej */}
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-sage transition-all duration-400 group-hover:w-full"></div>
                  </a>

                  {/* Eleganckie dropdown menu */}
                  {link.submenu && (
                    <div className="absolute top-full left-0 mt-2 bg-primary shadow-soft border border-text-gray/10 min-w-[220px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-400 transform translate-y-2 group-hover:translate-y-0 overflow-hidden">
                      <div className="py-3">
                        {link.submenu.map((sublink, index) => (
                          <a
                            key={sublink.href}
                            href={sublink.href}
                            className="block text-sm font-light text-text-gray hover:text-sage hover:bg-sage/5 transition-all duration-400 py-3 px-6 tracking-wide relative group/item"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            {sublink.label}
                            <div className="absolute left-0 top-1/2 w-0 h-0.5 bg-sage transition-all duration-400 group-hover/item:w-2 transform -translate-y-1/2"></div>
                          </a>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
              
              {/* CTA Button - Szałwiowy prostokąt */}
              <a
                href="/rezerwacja"
                className="bg-transparent border border-sage text-sage px-8 py-3 font-body font-normal text-sm tracking-[1px] hover:bg-sage hover:text-primary transition-all duration-400"
              >
                Zarezerwuj Miejsce
              </a>
            </nav>

            {/* Mobile Menu - Eleganckie */}
            <div className="lg:hidden">
              <details className="relative">
                <summary className="text-text-gray hover:text-sage cursor-pointer font-light text-sm tracking-wide transition-colors duration-400 py-2">
                  menu
                </summary>
                <nav className="absolute right-0 top-full mt-2 bg-primary shadow-soft border border-text-gray/10 p-6 min-w-[240px] z-50">
                  {staticNavLinks.map((link) => (
                    <div key={link.href} className="group">
                      <a
                        href={link.href}
                        className="block text-sm font-light text-text-gray hover:text-sage transition-colors duration-400 py-3 tracking-wide border-b border-text-gray/5 last:border-b-0"
                      >
                        {link.label}
                      </a>

                      {/* Mobile Submenu */}
                      {link.submenu && (
                        <div className="mt-2 ml-4 space-y-2">
                          {link.submenu.map((sublink) => (
                            <a
                              key={sublink.href}
                              href={sublink.href}
                              className="block text-xs font-light text-text-gray/80 hover:text-sage transition-colors duration-400 py-1"
                            >
                              {sublink.label}
                            </a>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Mobile CTA */}
                  <div className="mt-6 pt-4 border-t border-text-gray/10">
                    <a
                      href="/rezerwacja"
                      className="block bg-transparent border border-sage text-sage px-6 py-3 font-light text-sm tracking-wide hover:bg-sage hover:text-primary transition-all duration-400 text-center"
                    >
                      Zarezerwuj Miejsce
                    </a>
                  </div>

                  {/* Contact Info */}
                  <div className="mt-4 pt-4 border-t border-text-gray/10">
                    <a
                      href="tel:+48606101523"
                      className="block text-sm font-light text-text-gray hover:text-sage transition-colors duration-400 py-2"
                    >
                      +48 606 101 523
                    </a>
                  </div>
                </nav>
              </details>
            </div>
          </div>
        </div>
      </header>
    </>
  );
}