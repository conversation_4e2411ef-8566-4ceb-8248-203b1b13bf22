'use client';

import * as React from "react"

import { cn } from "@/lib/utils"

const Input = React.forwardRef(({ className, type, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        "flex h-9 w-full border border-sage/20 bg-transparent px-3 py-1 text-base shadow-soft transition-colors file:border-0 file:bg-transparent file:text-sm file:font-light file:text-foreground placeholder:text-text-gray/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-sage disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        className
      )}
      ref={ref}
      {...props} />
  );
})
Input.displayName = "Input"

export { Input }
