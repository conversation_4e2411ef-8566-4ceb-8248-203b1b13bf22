(()=>{var e={};e.id=7302,e.ids=[7302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6764:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=a(65239),i=a(48088),l=a(88170),n=a.n(l),r=a(30893),c={};for(let e in r)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>r[e]);a.d(s,c);let d={children:["",{children:["zajecia-online",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,59724)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\zajecia-online\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(a.bind(a,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\zajecia-online\\page.jsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/zajecia-online/page",pathname:"/zajecia-online",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},56080:(e,s,a)=>{Promise.resolve().then(a.bind(a,68444))},59724:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\app\\\\zajecia-online\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\zajecia-online\\page.jsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68444:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(60687),i=a(30474),l=a(43210),n=a(13508),r=a(8958);let c=({isOpen:e,onClose:s,classType:a="private"})=>{let[i,c]=(0,l.useState)(1),[d,o]=(0,l.useState)({firstName:"",lastName:"",email:"",phone:"",experience:"",preferences:"",classType:a,selectedPackage:"",preferredDates:[],specialRequests:"",agreeTerms:!1,agreeNewsletter:!1}),[m,p]=(0,l.useState)("blik"),[x,j]=(0,l.useState)(!1),h={private:[{id:"trial",name:"Sesja pr\xf3bna",duration:"30 min",price:80,description:"Idealna na początek"},{id:"single",name:"Pojedyncza sesja",duration:"60 min",price:150,description:"Elastyczna opcja"},{id:"package4",name:"Pakiet 4 sesji",duration:"60 min",price:550,savings:50,description:"Regularność za mniej"},{id:"package8",name:"Pakiet 8 sesji",duration:"60 min",price:1e3,savings:200,description:"Najlepsza wartość"}],group:[{id:"single-group",name:"Pojedyncze zajęcia",sessions:"1 zajęcia",price:45,description:"Wypr\xf3buj atmosferę grupy"},{id:"monthly",name:"Karnet miesięczny",sessions:"8 zajęć",price:280,savings:80,description:"Regularna praktyka"},{id:"quarterly",name:"Karnet kwartalny",sessions:"24 zajęcia",price:720,savings:360,description:"Długoterminowy rozw\xf3j"}]},g=e=>{let{name:s,value:a,type:t,checked:i}=e.target;o(e=>({...e,[s]:"checkbox"===t?i:a}))},u=e=>{o(s=>({...s,preferredDates:s.preferredDates.includes(e)?s.preferredDates.filter(s=>s!==e):[...s.preferredDates,e]}))},y=async e=>{if(e.preventDefault(),i<3)return void c(i+1);j(!0);try{let e=h[a].find(e=>e.id===d.selectedPackage),t=await fetch("/api/online-booking",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({formData:d,paymentMethod:m,package:e,classType:a,timestamp:new Date().toISOString()})});(await t.json()).success?(await fetch("/api/email-sequences",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sequenceType:"online_class_booking",recipientEmail:d.email,recipientName:`${d.firstName} ${d.lastName}`,customData:{classType:a,packageName:e.name,price:e.price,paymentMethod:m},triggerImmediately:!0})}),alert("Rezerwacja została złożona! Sprawdź email z dalszymi instrukcjami."),s()):alert("Wystąpił błąd. Spr\xf3buj ponownie lub skontaktuj się z nami.")}catch(e){console.error("Booking error:",e),alert("Wystąpił błąd. Spr\xf3buj ponownie lub skontaktuj się z nami.")}finally{j(!1)}},b=e=>new Date(e).toLocaleDateString("pl-PL",{weekday:"long",day:"numeric",month:"long"}),f=h[a].find(e=>e.id===d.selectedPackage);return e?(0,t.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:s,children:(0,t.jsx)(n.P.div,{initial:{scale:.9,y:50},animate:{scale:1,y:0},exit:{scale:.9,y:50},className:"bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsxs)("h3",{className:"text-2xl font-serif text-temple mb-2",children:["Rezerwacja ","private"===a?"sesji prywatnej":"zajęć grupowych"]}),(0,t.jsx)("div",{className:"flex items-center justify-center gap-4 mt-4",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${i>=e?"bg-temple text-white":"bg-gray-200 text-gray-500"}
                  `,children:e}),e<3&&(0,t.jsx)("div",{className:`w-8 h-0.5 mx-2 ${i>e?"bg-temple":"bg-gray-200"}`})]},e))}),(0,t.jsxs)("div",{className:"flex justify-center mt-2 text-sm text-wood-light",children:[(0,t.jsx)("span",{className:i>=1?"text-temple":"",children:"Szczeg\xf3ły"}),(0,t.jsx)("span",{className:"mx-4",children:"•"}),(0,t.jsx)("span",{className:i>=2?"text-temple":"",children:"Płatność"}),(0,t.jsx)("span",{className:"mx-4",children:"•"}),(0,t.jsx)("span",{className:i>=3?"text-temple":"",children:"Potwierdzenie"})]})]}),(0,t.jsxs)("form",{onSubmit:y,children:[(0,t.jsxs)(n.N,{mode:"wait",children:[1===i&&(0,t.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-medium text-temple mb-4",children:"Wybierz pakiet:"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h[a].map(e=>(0,t.jsxs)("div",{className:`
                            payment-method-card cursor-pointer
                            ${d.selectedPackage===e.id?"selected":""}
                          `,onClick:()=>o(s=>({...s,selectedPackage:e.id})),children:[e.savings&&(0,t.jsxs)("div",{className:"payment-method-popular",children:["Oszczędność ",e.savings," zł"]}),(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h5",{className:"font-medium text-temple",children:e.name}),(0,t.jsxs)("span",{className:"text-lg font-bold text-temple",children:[e.price," zł"]})]}),(0,t.jsx)("p",{className:"text-sm text-wood-light mb-2",children:e.duration||e.sessions}),(0,t.jsx)("p",{className:"text-xs text-wood-light",children:e.description})]},e.id))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Imię *"}),(0,t.jsx)("input",{type:"text",name:"firstName",value:d.firstName,onChange:g,required:!0,className:"waiting-list-input"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Nazwisko *"}),(0,t.jsx)("input",{type:"text",name:"lastName",value:d.lastName,onChange:g,required:!0,className:"waiting-list-input"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Email *"}),(0,t.jsx)("input",{type:"email",name:"email",value:d.email,onChange:g,required:!0,className:"waiting-list-input"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Telefon *"}),(0,t.jsx)("input",{type:"tel",name:"phone",value:d.phone,onChange:g,required:!0,className:"waiting-list-input"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Doświadczenie z jogą"}),(0,t.jsxs)("select",{name:"experience",value:d.experience,onChange:g,className:"waiting-list-input",children:[(0,t.jsx)("option",{value:"",children:"Wybierz poziom"}),(0,t.jsx)("option",{value:"beginner",children:"Początkujący (0-6 miesięcy)"}),(0,t.jsx)("option",{value:"intermediate",children:"Średniozaawansowany (6 miesięcy - 2 lata)"}),(0,t.jsx)("option",{value:"advanced",children:"Zaawansowany (2+ lata)"}),(0,t.jsx)("option",{value:"teacher",children:"Instruktor/ka jogi"})]})]}),"group"===a&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-4",children:"Preferowane terminy (wybierz kilka):"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[{date:"2025-01-08",time:"18:00",type:"Hatha Yoga",spots:3},{date:"2025-01-10",time:"19:00",type:"Vinyasa Flow",spots:2},{date:"2025-01-12",time:"17:30",type:"Yin Yoga",spots:5},{date:"2025-01-13",time:"10:00",type:"Joga dla plec\xf3w",spots:4},{date:"2025-01-15",time:"18:00",type:"Hatha Yoga",spots:6},{date:"2025-01-17",time:"19:00",type:"Vinyasa Flow",spots:1}].map(e=>(0,t.jsx)("div",{className:`
                              p-3 border rounded-lg cursor-pointer transition-all
                              ${d.preferredDates.includes(e.date)?"border-temple bg-temple/5":"border-gray-200 hover:border-temple/50"}
                            `,onClick:()=>u(e.date),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-temple text-sm",children:b(e.date)}),(0,t.jsxs)("p",{className:"text-xs text-wood-light",children:[e.time," • ",e.type]})]}),(0,t.jsxs)("span",{className:"text-xs text-golden bg-golden/10 px-2 py-1 rounded-full",children:[e.spots," miejsc"]})]})},e.date))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Dodatkowe informacje"}),(0,t.jsx)("textarea",{name:"specialRequests",value:d.specialRequests,onChange:g,rows:3,className:"waiting-list-input resize-none",placeholder:"Kontuzje, preferencje, cele..."})]})]},"step1"),2===i&&(0,t.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6",children:[f&&(0,t.jsxs)("div",{className:"bg-temple/5 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-temple mb-2",children:"Podsumowanie zam\xf3wienia:"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{children:f.name}),(0,t.jsxs)("span",{className:"font-bold",children:[f.price," zł"]})]}),f.savings&&(0,t.jsxs)("p",{className:"text-sm text-sage mt-1",children:["Oszczędzasz ",f.savings," zł!"]})]}),(0,t.jsx)(r.A,{selectedMethod:m,onPaymentMethodChange:p}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"flex items-start gap-3",children:[(0,t.jsx)("input",{type:"checkbox",name:"agreeTerms",checked:d.agreeTerms,onChange:g,required:!0,className:"mt-1"}),(0,t.jsx)("span",{className:"text-sm text-wood-light",children:"Akceptuję regulamin zajęć online i politykę prywatności *"})]}),(0,t.jsxs)("label",{className:"flex items-start gap-3",children:[(0,t.jsx)("input",{type:"checkbox",name:"agreeNewsletter",checked:d.agreeNewsletter,onChange:g,className:"mt-1"}),(0,t.jsx)("span",{className:"text-sm text-wood-light",children:"Chcę otrzymywać newsletter z informacjami o zajęciach i promocjach"})]})]})]},"step2"),3===i&&(0,t.jsxs)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},className:"space-y-6 text-center",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE4F"}),(0,t.jsx)("h4",{className:"text-xl font-serif text-temple mb-4",children:"Potwierdź rezerwację"}),(0,t.jsxs)("div",{className:"bg-temple/5 p-6 rounded-lg text-left",children:[(0,t.jsx)("h5",{className:"font-medium text-temple mb-4",children:"Szczeg\xf3ły rezerwacji:"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Pakiet:"}),(0,t.jsx)("span",{children:f?.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Cena:"}),(0,t.jsxs)("span",{className:"font-bold",children:[f?.price," zł"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Płatność:"}),(0,t.jsx)("span",{children:"blik"===m?"BLIK":"transfer"===m?"Przelew":"Fitssey"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{children:"Email:"}),(0,t.jsx)("span",{children:d.email})]})]})]}),(0,t.jsx)("p",{className:"text-sm text-wood-light",children:"Po potwierdzeniu otrzymasz email z instrukcjami płatności i linkiem do spotkania."})]},"step3")]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mt-8 pt-6 border-t border-gray-200",children:[i>1&&(0,t.jsx)("button",{type:"button",onClick:()=>c(i-1),className:"btn-unified-secondary flex-1",children:"Wstecz"}),(0,t.jsx)("button",{type:"submit",disabled:x||1===i&&!d.selectedPackage||2===i&&!d.agreeTerms,className:"btn-unified-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,t.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Przetwarzanie..."]}):3===i?"Potwierdź rezerwację":"Dalej"}),(0,t.jsx)("button",{type:"button",onClick:s,className:"btn-unified-secondary flex-1",children:"Anuluj"})]})]})]})})}):null};var d=a(64058);function o(){let[e,s]=(0,l.useState)(!1),[a,n]=(0,l.useState)("private");return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Zajęcia Jogi Online - Julia Jakubowicz",description:"Prywatne lekcje jogi online i grupowe zajęcia online z certyfikowaną instruktorką jogi RYT 500.",provider:{"@type":"Person",name:"Julia Jakubowicz"}})}}),(0,t.jsxs)("main",{className:"relative min-h-screen bg-gradient-to-b from-rice/90 via-mist/50 to-ocean-light/10",children:[(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-content container-padding",children:(0,t.jsxs)("div",{className:"section-title",children:[(0,t.jsx)("h1",{className:"text-4xl md:text-5xl font-serif text-temple mb-6 font-light",children:"Zajęcia Online"}),(0,t.jsx)("p",{className:"text-lg text-wood-light leading-relaxed font-light",children:"Praktykuj jogę z domu w komforcie i bezpieczeństwie"}),(0,t.jsx)("div",{className:"decorative-line"})]})})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-content container-padding",children:(0,t.jsx)("div",{className:"card p-12",children:(0,t.jsxs)("div",{className:"md:flex items-start gap-12",children:[(0,t.jsxs)("div",{className:"md:w-3/5 mb-8 md:mb-0",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif text-temple mb-4 font-light",children:"Prywatne Lekcje Jogi Online"}),(0,t.jsx)("p",{className:"text-wood-light leading-relaxed mb-8 font-light",children:"Spersonalizowane sesje jogi dostosowane do Twoich potrzeb i poziomu zaawansowania."}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg text-temple mb-4 font-light",children:"Co obejmuje sesja:"}),(0,t.jsx)("div",{className:"space-y-3",children:["Konsultacja wstępna i analiza potrzeb","Spersonalizowany program ćwiczeń","Korekty w czasie rzeczywistym","Nagranie sesji do powtarzania","Plan domowej praktyki między sesjami"].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"w-1 h-1 bg-temple/40 mt-2 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-wood-light leading-relaxed text-sm font-light",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg text-temple mb-4 font-light",children:"Cennik:"}),(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Sesja pr\xf3bna",duration:"30 min",price:"80 zł"},{name:"Pojedyncza sesja",duration:"60 min",price:"150 zł"},{name:"Pakiet 4 sesji",duration:"60 min",price:"550 zł",savings:"Oszczędność 50 zł"},{name:"Pakiet 8 sesji",duration:"60 min",price:"1000 zł",savings:"Oszczędność 200 zł"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-temple/10",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-temple font-light",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-wood-light",children:e.duration})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("span",{className:"text-temple font-light",children:e.price}),e.savings&&(0,t.jsx)("p",{className:"text-xs text-sage",children:e.savings})]})]},s))})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{n("private"),s(!0)},className:"btn-unified-primary w-full",children:"Zarezerwuj sesję prywatną"})})]}),(0,t.jsx)("div",{className:"md:w-2/5",children:(0,t.jsxs)("div",{className:"h-80 md:h-96 relative rounded-lg overflow-hidden",children:[(0,t.jsx)(i.default,{src:"/images/profile/omnie-opt.webp",alt:"Prywatne lekcje jogi online",fill:!0,className:"object-cover",sizes:"(max-width: 768px) 100vw, 40vw"}),(0,t.jsx)("div",{className:"absolute inset-0 bg-temple/5"})]})})]})})})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-content container-padding",children:(0,t.jsx)("div",{className:"card p-12",children:(0,t.jsxs)("div",{className:"md:flex items-start gap-12 md:flex-row-reverse",children:[(0,t.jsxs)("div",{className:"md:w-3/5 mb-8 md:mb-0",children:[(0,t.jsx)("h2",{className:"text-3xl font-serif text-temple mb-4 font-light",children:"Grupowe Zajęcia Online"}),(0,t.jsx)("p",{className:"text-wood-light leading-relaxed mb-8 font-light",children:"Zajęcia jogi w małych grupach dla lepszej atmosfery i indywidualnego podejścia."}),(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h3",{className:"text-lg text-temple mb-4 font-light",children:"Harmonogram zajęć:"}),(0,t.jsx)("div",{className:"space-y-3",children:[{day:"Poniedziałek",time:"18:00",type:"Hatha Yoga",level:"początkujący"},{day:"Środa",time:"19:00",type:"Vinyasa Flow",level:"średniozaawansowani"},{day:"Piątek",time:"17:30",type:"Yin Yoga",level:"relaks"},{day:"Sobota",time:"10:00",type:"Joga dla plec\xf3w",level:"wszystkie poziomy"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-temple/10",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-temple font-light",children:[e.day," ",e.time]}),(0,t.jsx)("p",{className:"text-xs text-wood-light",children:e.type})]}),(0,t.jsx)("span",{className:"text-xs text-golden/80 bg-golden/5 px-2 py-1 rounded-full",children:e.level})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg text-temple mb-4 font-light",children:"Opcje cenowe:"}),(0,t.jsx)("div",{className:"space-y-3",children:[{name:"Małe grupy",size:"4-6 os\xf3b",price:"45 zł/zajęcia"},{name:"Średnie grupy",size:"7-12 os\xf3b",price:"35 zł/zajęcia"},{name:"Karnet miesięczny",size:"8 zajęć",price:"280 zł"},{name:"Karnet kwartalny",size:"24 zajęcia",price:"720 zł"}].map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-temple/10",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-temple font-light",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-wood-light",children:e.size})]}),(0,t.jsx)("span",{className:"text-temple font-light",children:e.price})]},s))})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{n("group"),s(!0)},className:"btn-unified-primary w-full",children:"Zapisz się na zajęcia grupowe"})})]}),(0,t.jsx)("div",{className:"md:w-2/5",children:(0,t.jsx)("div",{className:"h-80 md:h-96 bg-temple/5 flex items-center justify-center rounded-lg",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-4xl mb-2 text-temple/40",children:"\uD83E\uDDD8‍♀️"}),(0,t.jsx)("span",{className:"text-temple/60 text-lg font-serif font-light",children:"Zajęcia Grupowe"})]})})})]})})})}),(0,t.jsx)("section",{className:"section section-padding",children:(0,t.jsx)("div",{className:"max-width-content container-padding",children:(0,t.jsxs)("div",{className:"glass-effect bg-gradient-to-r from-bamboo/10 to-lotus/20 rounded-3xl p-12 text-center",children:[(0,t.jsx)("h3",{className:"text-2xl font-serif text-temple mb-6 font-light",children:"Gotowa na pierwszą sesję?"}),(0,t.jsx)("p",{className:"text-wood-light leading-relaxed mb-8 font-light",children:"Skontaktuj się ze mną, aby um\xf3wić pierwszą sesję lub dołączyć do zajęć grupowych."}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,t.jsx)(d.P.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:()=>{n("private"),s(!0)},className:"btn-soft-golden","aria-label":"Zarezerwuj sesję online",children:"Zarezerwuj Sesję Online"}),(0,t.jsx)("span",{className:"text-temple/50 text-sm",children:"lub"}),(0,t.jsx)("a",{href:"https://app.fitssey.com/Flywithbakasana/frontoffice",target:"_blank",rel:"noopener noreferrer",className:"btn-soft","aria-label":"Um\xf3w przez Fitssey",children:"Um\xf3w przez Fitssey"})]})]})})}),(0,t.jsx)(c,{isOpen:e,onClose:()=>s(!1),classType:a})]})]})}},75555:(e,s,a)=>{Promise.resolve().then(a.bind(a,59724))}};var s=require("../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>a(6764));module.exports=t})();