"use strict";(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42129:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var a={};t.r(a),t.d(a,{default:()=>u});var i={};t.r(i),t.d(i,{GET:()=>c});var o=t(96559),n=t(48088),s=t(37719),l=t(32190);t(66945);var d=t(1830);function u(){let e="https://bakasana-travel.blog",r=[{url:e,lastModified:new Date,changeFrequency:"weekly",priority:1},{url:`${e}/program`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/program?destination=bali`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/program?destination=srilanka`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/zajecia-online`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/o-mnie`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/galeria`,lastModified:new Date,changeFrequency:"weekly",priority:.8},{url:`${e}/blog`,lastModified:new Date,changeFrequency:"weekly",priority:.7},{url:`${e}/kontakt`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${e}/blog/stanie-na-rekach-odkryj-swoja-wewnetrzna-sile-i-odwage`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${e}/blog/szpagaty-otworz-biodra-i-uwolnij-swoja-kobiecosc`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${e}/blog/kobieca-sila-w-jodze-odkryj-swoja-wewnetrzna-boginie`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${e}/blog/sri-lanka-perla-oceanu-indyjskiego-przewodnik-jogina`,lastModified:new Date,changeFrequency:"monthly",priority:.6},{url:`${e}/blog/ayurveda-joga-holistyczne-uzdrowienie-sri-lanka`,lastModified:new Date,changeFrequency:"monthly",priority:.6}],t=(0,d.XI)(),a=t.map(r=>({url:`${e}/program/${r.id}`,lastModified:new Date,changeFrequency:"monthly",priority:.8})),i=[];return t.forEach(r=>{(0,d.IP)(r.id).forEach(r=>{i.push({url:`${e}/program/${r.slug}`,lastModified:new Date,changeFrequency:"monthly",priority:.5})})}),[...r,...a,...i]}var p=t(12127);let y={...a}.default;if("function"!=typeof y)throw Error('Default export is missing in "C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\sitemap.js"');async function c(e,r){let{__metadata_id__:t,...a}=await r.params||{},i=!!t&&t.endsWith(".xml");if(t&&!i)return new l.NextResponse("Not Found",{status:404});let o=t&&i?t.slice(0,-4):void 0,n=await y({id:o}),s=(0,p.resolveRouteData)(n,"sitemap");return new l.NextResponse(s,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let g=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_prod%5Cmy-travel-blog%5Csrc%5Capp%5Csitemap.js&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:w}=g;function f(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>t(42129));module.exports=a})();