"use strict";(()=>{var e={};e.id=1676,e.ids=[1676],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80829:e=>{e.exports=require("jsonwebtoken")},92371:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>j,serverHooks:()=>z,workAsyncStorage:()=>k,workUnitAsyncStorage:()=>x});var a={};s.r(a),s.d(a,{DELETE:()=>g,GET:()=>m,PATCH:()=>w});var t=s(96559),n=s(48088),o=s(37719),i=s(32190),u=s(80829),d=s.n(u);let c=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production",p=[{id:"1",firstName:"Anna",lastName:"Kowalska",email:"<EMAIL>",phone:"+48 123 456 789",program:"Retreat Jogi",destination:"Bali",status:"pending",createdAt:new Date("2024-12-15T10:30:00Z").toISOString(),message:"Bardzo się cieszę na ten retreat!"},{id:"2",firstName:"Marcin",lastName:"Nowak",email:"<EMAIL>",phone:"+48 987 654 321",program:"Ayurveda & Joga",destination:"Sri Lanka",status:"confirmed",createdAt:new Date("2024-12-14T14:15:00Z").toISOString(),message:"Czy mogę dostać informacje o diecie?"},{id:"3",firstName:"Katarzyna",lastName:"Wiśniewska",email:"<EMAIL>",phone:"+48 555 123 456",program:"Detox & Mindfulness",destination:"Tajlandia",status:"pending",createdAt:new Date("2024-12-13T09:45:00Z").toISOString(),message:"Pierwszy raz na takim retrecie, jestem podekscytowana!"}];function l(e){let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return{valid:!1,error:"Brak tokenu autoryzacji"};let s=r.substring(7);try{let e=d().verify(s,c,{issuer:"bakasana-travel-admin",audience:"bakasana-travel-app"});if("admin"!==e.role)return{valid:!1,error:"Niewystarczające uprawnienia"};return{valid:!0,user:e}}catch(e){return{valid:!1,error:"Nieprawidłowy token"}}}async function m(e,{params:r}){try{let s=l(e);if(!s.valid)return i.NextResponse.json({success:!1,error:s.error},{status:401});let{id:a}=r,t=p.find(e=>e.id===a);if(!t)return i.NextResponse.json({success:!1,error:"Rezerwacja nie została znaleziona"},{status:404});return i.NextResponse.json({success:!0,booking:t})}catch(e){return console.error("Admin booking GET error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas pobierania rezerwacji",details:void 0},{status:500})}}async function w(e,{params:r}){try{let s=l(e);if(!s.valid)return i.NextResponse.json({success:!1,error:s.error},{status:401});let{id:a}=r,t=await e.json(),n=p.findIndex(e=>e.id===a);if(-1===n)return i.NextResponse.json({success:!1,error:"Rezerwacja nie została znaleziona"},{status:404});if(t.status&&!["pending","confirmed","cancelled"].includes(t.status))return i.NextResponse.json({success:!1,error:"Nieprawidłowy status rezerwacji"},{status:400});return p[n].status,p[n]={...p[n],...t,updatedAt:new Date().toISOString(),updatedBy:s.user.ip||"admin"},t.status&&t.status,i.NextResponse.json({success:!0,booking:p[n],message:"Rezerwacja została zaktualizowana pomyślnie"})}catch(e){return console.error("Admin booking PATCH error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas aktualizacji rezerwacji",details:void 0},{status:500})}}async function g(e,{params:r}){try{let s=l(e);if(!s.valid)return i.NextResponse.json({success:!1,error:s.error},{status:401});let{id:a}=r,t=p.findIndex(e=>e.id===a);if(-1===t)return i.NextResponse.json({success:!1,error:"Rezerwacja nie została znaleziona"},{status:404});let n=p.splice(t,1)[0];return i.NextResponse.json({success:!0,message:"Rezerwacja została usunięta pomyślnie",deletedBooking:n})}catch(e){return console.error("Admin booking DELETE error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas usuwania rezerwacji",details:void 0},{status:500})}}let j=new t.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/bookings/[id]/route",pathname:"/api/admin/bookings/[id]",filename:"route",bundlePath:"app/api/admin/bookings/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\api\\admin\\bookings\\[id]\\route.js",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:k,workUnitAsyncStorage:x,serverHooks:z}=j;function f(){return(0,o.patchFetch)({workAsyncStorage:k,workUnitAsyncStorage:x})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>s(92371));module.exports=a})();