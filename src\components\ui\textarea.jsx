'use client';

import * as React from "react"

import { cn } from "@/lib/utils"

const Textarea = React.forwardRef(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        "flex min-h-[60px] w-full border border-sage/20 bg-transparent px-3 py-2 text-base shadow-soft placeholder:text-text-gray/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-sage disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
