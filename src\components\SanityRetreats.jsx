import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getRetreats, urlFor, formatDate, formatPrice } from '../../lib/sanity';

// Fallback retreats jeśli Sanity nie jest jeszcze skonfigurowane
const fallbackRetreats = [
  {
    _id: 'fallback-1',
    title: 'Retreat Jogowy Bali - Marzec 2025',
    slug: { current: 'retreat-bali-marzec-2025' },
    shortDescription: 'Od<PERSON><PERSON>j magię Bali podczas 12-dniowego retreatu jogowego. Ubud, Nusa Penida, Gili Air.',
    startDate: '2025-03-15',
    endDate: '2025-03-27',
    price: 3200,
    currency: 'PLN',
    maxParticipants: 12,
    currentParticipants: 8,
    status: 'available',
    location: {
      country: 'Indonezja',
      region: 'Bali',
      places: ['Ubud', 'Nusa Penida', 'Gili Air']
    },
    highlights: [
      'Codzienne zajęcia jogi i medytacji',
      '<PERSON>wi<PERSON><PERSON><PERSON> tarasów ryżowych',
      'Snorkeling na Nusa Penida',
      'Relaks na Gili Air'
    ]
  },
  {
    _id: 'fallback-2',
    title: 'Retreat Jogowy Bali - Maj 2025',
    slug: { current: 'retreat-bali-maj-2025' },
    shortDescription: 'Wiosenny retreat w najpiękniejszych miejscach Bali. Idealne połączenie jogi i przygody.',
    startDate: '2025-05-10',
    endDate: '2025-05-22',
    price: 2900,
    currency: 'PLN',
    maxParticipants: 12,
    currentParticipants: 3,
    status: 'available',
    location: {
      country: 'Indonezja',
      region: 'Bali',
      places: ['Ubud', 'Uluwatu', 'Canggu']
    },
    highlights: [
      'Joga na klifach Uluwatu',
      'Surfing w Canggu',
      'Warsztaty mindfulness',
      'Ceremonie w świątyniach'
    ]
  }
];

const StatusBadge = ({ status, currentParticipants, maxParticipants }) => {
  const getStatusInfo = () => {
    switch (status) {
      case 'available':
        const spotsLeft = maxParticipants - currentParticipants;
        if (spotsLeft <= 3) {
          return { text: `Ostatnie ${spotsLeft} miejsca`, color: 'bg-warm-gold/80 text-accent' };
        }
        return { text: 'Dostępny', color: 'bg-accent/10 text-accent border border-accent/20' };
      case 'last_spots':
        return { text: 'Ostatnie miejsca', color: 'bg-warm-gold/80 text-accent' };
      case 'sold_out':
        return { text: 'Wyprzedany', color: 'bg-accent/80 text-primary' };
      case 'cancelled':
        return { text: 'Anulowany', color: 'bg-accent/40 text-accent' };
      default:
        return { text: 'Dostępny', color: 'bg-accent/10 text-accent border border-accent/20' };
    }
  };

  const { text, color } = getStatusInfo();

  return (
    <span className={`inline-block px-3 py-1 text-sm font-light ${color}`}>
      {text}
    </span>
  );
};

const RetreatCard = ({ retreat }) => {
  const spotsLeft = retreat.maxParticipants - retreat.currentParticipants;
  const isAvailable = retreat.status === 'available' && spotsLeft > 0;

  return (
    <div className="bg-primary/95 backdrop-blur-sm border border-sage/10 overflow-hidden hover:border-sage/20 transition-all duration-400 hover:-translate-y-1 group shadow-soft">
      {/* Obrazek */}
      <div className="relative h-64 overflow-hidden">
        {retreat.images && retreat.images[0] && urlFor(retreat.images[0]) ? (
          <Image
            src={urlFor(retreat.images[0]).width(400).height(256).url()}
            alt={retreat.images[0].alt || retreat.title}
            fill
            className="object-cover transition-transform duration-400"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-sage/20 to-sand/30 flex items-center justify-center">
            <span className="text-sage/60 text-lg font-display">Bali Yoga Retreat</span>
          </div>
        )}
        
        {/* Status badge */}
        <div className="absolute top-4 left-4">
          <StatusBadge 
            status={retreat.status} 
            currentParticipants={retreat.currentParticipants}
            maxParticipants={retreat.maxParticipants}
          />
        </div>
      </div>

      {/* Treść */}
      <div className="p-6">
        <h3 className="text-xl font-serif text-temple mb-3 font-light">
          {retreat.title}
        </h3>
        
        <p className="text-wood-light/80 mb-4 leading-relaxed">
          {retreat.shortDescription}
        </p>

        {/* Daty */}
        <div className="flex items-center gap-2 mb-3 text-sm text-wood-light/70">
          <span>📅</span>
          <span>{formatDate(retreat.startDate)} - {formatDate(retreat.endDate)}</span>
        </div>

        {/* Lokalizacja */}
        <div className="flex items-center gap-2 mb-3 text-sm text-wood-light/70">
          <span>📍</span>
          <span>{retreat.location?.places?.join(', ') || 'Bali, Indonezja'}</span>
        </div>

        {/* Miejsca */}
        <div className="flex items-center gap-2 mb-4 text-sm text-wood-light/70">
          <span>👥</span>
          <span>{retreat.currentParticipants}/{retreat.maxParticipants} uczestników</span>
        </div>

        {/* Cena */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <span className="text-2xl font-light text-temple font-serif">
              {formatPrice(retreat.price, retreat.currency)}
            </span>
            <span className="text-sm text-wood-light/60 ml-2">/ osoba</span>
          </div>
        </div>

        {/* Przyciski */}
        <div className="flex gap-3">
          <Link
            href={`/program/${retreat.slug?.current || retreat._id}`}
            className="flex-1 bg-temple text-white text-center py-3 px-4 rounded-full hover:bg-temple/90 transition-colors font-medium"
          >
            Zobacz szczegóły
          </Link>
          
          {isAvailable && (
            <Link
              href="/kontakt"
              className="flex-1 border border-temple text-temple text-center py-3 px-4 rounded-full hover:bg-temple/10 transition-colors font-medium"
            >
              Zapisz się
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

const SanityRetreats = async () => {
  let retreats = [];
  
  try {
    // Spróbuj pobrać retreaty z Sanity
    retreats = await getRetreats();
    
    // Filtruj tylko dostępne i przyszłe retreaty
    const now = new Date();
    retreats = retreats.filter(retreat => {
      const startDate = new Date(retreat.startDate);
      return startDate > now && retreat.status !== 'hidden';
    });
    
    // Jeśli nie ma retreatów w Sanity, użyj fallback
    if (!retreats || retreats.length === 0) {
      retreats = fallbackRetreats;
    }
  } catch (error) {
    console.log('Sanity not configured yet, using fallback retreats');
    retreats = fallbackRetreats;
  }

  return (
    <section className="py-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <div className="subtitle mb-4">Nadchodzące wyjazdy</div>
          <h2 className="text-3xl md:text-4xl font-serif text-temple mb-4">
            Retreaty Jogowe na Bali 2025
          </h2>
          <p className="text-wood-light/80 text-lg mb-8">
            Wybierz swój idealny termin i dołącz do transformacyjnej podróży
          </p>
          <div className="w-24 h-px bg-temple/20 mx-auto"></div>
        </div>

        {retreats.length > 0 ? (
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3">
            {retreats.map((retreat, index) => (
              <div
                key={retreat._id}
                className="animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <RetreatCard retreat={retreat} />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-wood-light/60 text-lg mb-6">
              Obecnie przygotowujemy nowe terminy retreatów na 2025 rok.
            </p>
            <Link
              href="/kontakt"
              className="inline-block bg-temple text-white px-8 py-3 rounded-full hover:bg-temple/90 transition-colors"
            >
              Zapytaj o dostępne terminy
            </Link>
          </div>
        )}

        {/* CTA */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-temple/10 to-bamboo/10 rounded-2xl p-8">
            <h3 className="text-2xl font-serif text-temple mb-4">
              Nie znalazłeś idealnego terminu?
            </h3>
            <p className="text-wood-light/80 mb-6">
              Skontaktuj się z nami - organizujemy również retreaty na indywidualne zamówienie.
            </p>
            <Link
              href="/kontakt"
              className="inline-block bg-temple text-white px-8 py-3 rounded-full hover:bg-temple/90 transition-colors"
            >
              Skontaktuj się z nami
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SanityRetreats;
