import React from 'react';
import Image from 'next/image';
import { generateMetadata as generateSEOMetadata, generateStructuredData } from '../metadata';

export const metadata = generateSEOMetadata({
  title: '<PERSON> - Instruktorka Jogi i Fizjoterapeutka',
  description: 'Poznaj Jul<PERSON> - magistra fizjoterapii z 8-letnim doświadczeniem i certyfikowaną instruktorkę jogi RYT 500. Organizatorka retreatów jogowych na Bali od 2020 roku.',
  keywords: [
    '<PERSON>',
    'instruktorka jogi RYT 500',
    'fizjoterapeutka magister',
    'joga terapeutyczna',
    'organizator retreatów Bali',
    'Yoga Alliance',
    'doświadczenie fizjoterapia',
    'holistyczne podejście joga'
  ],
});

export default function OMniePage() {
  const structuredData = generateStructuredData({
    type: 'Person',
    name: '<PERSON>',
    description: 'Magister fiz<PERSON><PERSON>pi<PERSON> i certyfikowana instruktorka jogi RYT 500. Organizatorka retreatów jogowych na Bali.',
  });

  const qualifications = [
    'Magister fizjoterapii z 8-letnim doświadczeniem klinicznym',
    'Certyfikowana instruktorka jogi (RYT 500) - Yoga Alliance',
    'Specjalizacja w jodze terapeutycznej i rehabilitacyjnej',
    'Ukończone kursy: Vinyasa, Hatha, Yin Yoga, Pranayama',
    'Organizator retreatów jogowych na Bali od 2020 roku',
    'Autorka programów łączących fizjoterapię z praktyką jogi',
    'Współpraca z ośrodkami rehabilitacyjnymi i studiami jogi',
    'Certyfikat w zakresie anatomii i biomechaniki ruchu'
  ];

  const achievements = [
    {
      title: 'Ponad 2000 godzin nauczania jogi',
      description: 'Doświadczenie w pracy z różnymi grupami wiekowymi i poziomami zaawansowania',
      category: 'Doświadczenie'
    },
    {
      title: 'Kilka udanych retreatów na Bali',
      description: 'Zorganizowane transformacyjne wyjazdy dla uczestników z całej Polski',
      category: 'Retreaty'
    },
    {
      title: 'Autorska metodyka łączenia fizjoterapii z jogą',
      description: 'Innowacyjne podejście do terapii bólu pleców i problemów postawy',
      category: 'Specjalizacja'
    },
    {
      title: 'Współpraca z ekspertami',
      description: 'Stała współpraca z lekarzami ortopedami i fizjoterapeutami',
      category: 'Partnerstwa'
    }
  ];

  const philosophy = [
    {
      title: 'Holistyczne podejście',
      description: 'Łączę wiedzę medyczną z duchową praktyką jogi, tworząc kompleksowe programy zdrowotne.',
      category: 'Metodyka'
    },
    {
      title: 'Indywidualizacja',
      description: 'Każdy uczestnik otrzymuje personalne wskazówki dostosowane do jego potrzeb i możliwości.',
      category: 'Podejście'
    },
    {
      title: 'Bezpieczeństwo przede wszystkim',
      description: 'Moje doświadczenie fizjoterapeutyczne gwarantuje bezpieczną praktykę dla wszystkich.',
      category: 'Priorytet'
    },
    {
      title: 'Transformacja przez podróż',
      description: 'Wierzę, że połączenie jogi z magią Bali tworzy przestrzeń dla głębokiej przemiany.',
      category: 'Wizja'
    }
  ];

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      <main className="relative min-h-screen bg-secondary">
        {/* Hero Section */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="section-title">
              <h1 className="text-3xl md:text-4xl font-serif text-temple mb-8 font-light">
                Julia Jakubowicz
              </h1>
              <p className="text-lg text-wood-light font-light max-w-2xl mx-auto">
                Poznaj moją drogę od fizjoterapii do instruktorki jogi i organizatorki transformacyjnych retreatów na Bali
              </p>
              <div className="decorative-line" />
            </div>
          </div>
        </section>

        {/* Main Profile Section */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="card p-8 md:p-12">
              <div className="text-center mb-12">
                {/* Section Label */}
                <span className="text-xs font-body font-light tracking-[4px] uppercase mb-4 block" style={{ color: '#7C9885' }}>
                  POZNAJ MNIE
                </span>

                <h2 className="text-2xl font-display font-normal mb-6" style={{ color: '#2C3E50' }}>
                  Pasja do holistycznego zdrowia
                </h2>

                {/* Decorative Line */}
                <div className="w-16 h-px mb-8 mx-auto" style={{ backgroundColor: '#7C9885' }}></div>
                
                <div className="w-48 mx-auto mb-8 relative aspect-[4/5] overflow-hidden shadow-soft">
                  <Image
                    src="/images/profile/omnie-opt.webp"
                    alt="Julia Jakubowicz - Instruktorka jogi i fizjoterapeutka"
                    fill
                    priority
                    className="object-cover"
                    sizes="192px"
                  />

                  {/* Experience Badge Overlay */}
                  <div className="absolute bottom-4 right-4 bg-primary p-4 shadow-elegant">
                    <div className="text-2xl font-display font-medium" style={{ color: '#7C9885' }}>8+</div>
                    <div className="text-xs font-body font-light tracking-wide" style={{ color: '#666666' }}>lat doświadczenia</div>
                  </div>
                </div>
              </div>

              <div className="space-y-6 font-light" style={{ color: '#666666' }}>
                <p className="text-[17px] leading-[1.8]">
                  Jestem magistrem fizjoterapii z 8-letnim doświadczeniem klinicznym oraz certyfikowaną
                  instruktorką jogi (RYT 500). Moją pasją jest łączenie wiedzy medycznej z duchową
                  praktyką jogi, tworząc holistyczne podejście do zdrowia i dobrostanu.
                </p>

                {/* Credentials List */}
                <div className="space-y-4 my-8">
                  <div className="flex items-start gap-4">
                    <div className="w-5 h-5 border flex items-center justify-center mt-1" style={{ borderColor: '#7C9885' }}>
                      <div className="w-2 h-2" style={{ backgroundColor: '#7C9885' }}></div>
                    </div>
                    <div>
                      <div className="font-body font-medium" style={{ color: '#2C3E50' }}>Magister Fizjoterapii</div>
                      <div className="font-body font-light text-sm" style={{ color: '#666666' }}>8 lat doświadczenia klinicznego</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-5 h-5 border flex items-center justify-center mt-1" style={{ borderColor: '#7C9885' }}>
                      <div className="w-2 h-2" style={{ backgroundColor: '#7C9885' }}></div>
                    </div>
                    <div>
                      <div className="font-body font-medium" style={{ color: '#2C3E50' }}>Certyfikowana Instruktorka Jogi</div>
                      <div className="font-body font-light text-sm" style={{ color: '#666666' }}>RYT 500 - Yoga Alliance</div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-5 h-5 border flex items-center justify-center mt-1" style={{ borderColor: '#7C9885' }}>
                      <div className="w-2 h-2" style={{ backgroundColor: '#7C9885' }}></div>
                    </div>
                    <div>
                      <div className="font-body font-medium" style={{ color: '#2C3E50' }}>Organizatorka Retreatów</div>
                      <div className="font-body font-light text-sm" style={{ color: '#666666' }}>Bali od 2020 roku</div>
                    </div>
                  </div>
                </div>

                <p className="text-[17px] leading-[1.8]">
                  Organizuję retreaty jogowe na Bali, gdzie dzielę się nie tylko technikami
                  jogi, ale także magią tej wyjątkowej wyspy. Moje programy łączą tradycyjną praktykę
                  z nowoczesną wiedzą o anatomii i biomechanice.
                </p>

                <p className="text-[17px] leading-[1.8]">
                  W Polsce prowadzę <a href="https://flywithbakasana.pl/" target="_blank" rel="noopener noreferrer" className="transition-colors" style={{ color: '#7C9885' }}>Studio Bakasana w Rzeszowie</a>,
                  gdzie regularnie uczę różnych stylów jogi: Hatha, Vinyasa, Ashtanga Flow, jogi dla kobiet w ciąży oraz seniorów.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Qualifications */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="section-title">
              <h3 className="text-xl font-serif text-temple mb-6 font-light">
                Kwalifikacje i Doświadczenie
              </h3>
              <p className="text-primary/70 mb-8 font-light">Profesjonalne wykształcenie i certyfikaty</p>
              <div className="decorative-line" />
            </div>

            <div className="card p-6">
              <div className="space-y-4">
                {qualifications.map((qualification, index) => (
                  <div key={index} className="flex items-start gap-3 py-3 border-b border-temple/5 last:border-b-0">
                    <div className="w-1 h-1 bg-temple/30 mt-3 flex-shrink-0 rounded-full" />
                    <span className="text-primary/80 font-light">
                      {qualification}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Achievements */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="section-title">
              <h3 className="text-xl font-serif text-temple mb-6 font-light">
                Profesjonalne Osiągnięcia
              </h3>
              <p className="text-primary/70 mb-8 font-light">Moje najważniejsze sukcesy zawodowe</p>
              <div className="decorative-line" />
            </div>

            <div className="space-y-6">
              {achievements.map((achievement, index) => (
                <div key={index} className="card p-6">
                  <h4 className="text-lg font-serif text-temple mb-3 font-light">
                    {achievement.title}
                  </h4>
                  <p className="text-primary/80 font-light">
                    {achievement.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Philosophy */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="section-title">
              <h3 className="text-xl font-serif text-temple mb-6 font-light">
                Filozofia Pracy
              </h3>
              <p className="text-primary/70 mb-8 font-light">Wartości które kierują moją praktyką</p>
              <div className="decorative-line" />
            </div>

            <div className="space-y-6">
              {philosophy.map((item, index) => (
                <div key={index} className="card p-6">
                  <h4 className="text-lg font-serif text-temple mb-3 font-light">
                    {item.title}
                  </h4>
                  <p className="text-primary/80 font-light">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="section section-padding">
          <div className="max-width-text container-padding">
            <div className="glass-effect rounded-2xl p-8 text-center">
              <h3 className="text-xl font-serif text-temple mb-6 font-light">
                Gotowa na transformację?
              </h3>

              <p className="text-primary/70 mb-8 font-light max-w-lg mx-auto">
                Dołącz do mnie na Bali i odkryj, jak joga może zmienić Twoje życie.
                Każdy retreat to unikalna podróż łącząca praktykę z magią wyspy.
              </p>

              <div className="flex flex-col gap-4 justify-center items-center">
                <a
                  href="/program"
                  className="btn-primary"
                  aria-label="Zobacz programy retreatów jogowych na Bali"
                >
                  Zobacz Programy Retreatów
                </a>

                <span className="text-sm font-light" style={{ color: '#7C9885', opacity: 0.5 }}>lub</span>

                <a
                  href="/kontakt"
                  className="btn-secondary"
                  aria-label="Skontaktuj się z Julią"
                >
                  Skontaktuj się ze mną
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  );
}