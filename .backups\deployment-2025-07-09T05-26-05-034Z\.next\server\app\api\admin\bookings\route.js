"use strict";(()=>{var e={};e.id=1206,e.ids=[1206],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80829:e=>{e.exports=require("jsonwebtoken")},90159:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>k,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>x});var a={};t.r(a),t.d(a,{GET:()=>m,POST:()=>g});var s=t(96559),n=t(48088),o=t(37719),i=t(32190),d=t(80829),u=t.n(d);let c=process.env.JWT_SECRET||"your-super-secret-jwt-key-change-this-in-production",p=[{id:"1",firstName:"Anna",lastName:"Kowalska",email:"<EMAIL>",phone:"+48 123 456 789",program:"Retreat Jogi",destination:"Bali",status:"pending",createdAt:new Date("2024-12-15T10:30:00Z").toISOString(),message:"Bardzo się cieszę na ten retreat!"},{id:"2",firstName:"Marcin",lastName:"Nowak",email:"<EMAIL>",phone:"+48 987 654 321",program:"Ayurveda & Joga",destination:"Sri Lanka",status:"confirmed",createdAt:new Date("2024-12-14T14:15:00Z").toISOString(),message:"Czy mogę dostać informacje o diecie?"},{id:"3",firstName:"Katarzyna",lastName:"Wiśniewska",email:"<EMAIL>",phone:"+48 555 123 456",program:"Detox & Mindfulness",destination:"Tajlandia",status:"pending",createdAt:new Date("2024-12-13T09:45:00Z").toISOString(),message:"Pierwszy raz na takim retrecie, jestem podekscytowana!"}];function l(e){let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return{valid:!1,error:"Brak tokenu autoryzacji"};let t=r.substring(7);try{let e=u().verify(t,c,{issuer:"bakasana-travel-admin",audience:"bakasana-travel-app"});if("admin"!==e.role)return{valid:!1,error:"Niewystarczające uprawnienia"};return{valid:!0,user:e}}catch(e){return{valid:!1,error:"Nieprawidłowy token"}}}async function m(e){try{let r=l(e);if(!r.valid)return i.NextResponse.json({success:!1,error:r.error},{status:401});let t=[...p].sort((e,r)=>new Date(r.createdAt)-new Date(e.createdAt));return i.NextResponse.json({success:!0,bookings:t,total:p.length,stats:{pending:p.filter(e=>"pending"===e.status).length,confirmed:p.filter(e=>"confirmed"===e.status).length,cancelled:p.filter(e=>"cancelled"===e.status).length}})}catch(e){return console.error("Admin bookings GET error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas pobierania rezerwacji",details:void 0},{status:500})}}async function g(e){try{let r=l(e);if(!r.valid)return i.NextResponse.json({success:!1,error:r.error},{status:401});let t=await e.json();for(let e of["firstName","lastName","email","program","destination"])if(!t[e])return i.NextResponse.json({success:!1,error:`Pole ${e} jest wymagane`},{status:400});let a={id:Date.now().toString(),...t,status:t.status||"pending",createdAt:new Date().toISOString()};return p.push(a),i.NextResponse.json({success:!0,booking:a,message:"Rezerwacja została dodana pomyślnie"})}catch(e){return console.error("Admin bookings POST error:",e),i.NextResponse.json({success:!1,error:"Błąd serwera podczas dodawania rezerwacji",details:void 0},{status:500})}}let k=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/bookings/route",pathname:"/api/admin/bookings",filename:"route",bundlePath:"app/api/admin/bookings/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\api\\admin\\bookings\\route.js",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:w,workUnitAsyncStorage:x,serverHooks:f}=k;function j(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:x})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>t(90159));module.exports=a})();