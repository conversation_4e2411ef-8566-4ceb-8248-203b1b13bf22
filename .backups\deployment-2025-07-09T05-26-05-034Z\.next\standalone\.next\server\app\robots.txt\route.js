"use strict";(()=>{var e={};e.id=3784,e.ids=[3784],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},18852:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>c,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var a={};r.r(a),r.d(a,{GET:()=>u});var o=r(96559),s=r(48088),n=r(37719),i=r(32190),p=r(12127);async function u(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/api/","/admin/","/_next/","/private/"]},sitemap:"https://bakasana-travel.blog/sitemap.xml",host:"https://bakasana-travel.blog"},t=(0,p.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let d=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cdavid%5CDesktop%5CProjekty%5Cblog_prod%5Cmy-travel-blog%5Csrc%5Capp%5Crobots.js&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:c}=d;function m(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>r(18852));module.exports=a})();