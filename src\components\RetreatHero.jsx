'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, Users, Star } from 'lucide-react';

const RetreatHero = () => {
  const [mounted, setMounted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [contentVisible, setContentVisible] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Delay content animation slightly for better effect
    const timer = setTimeout(() => setContentVisible(true), 500);
    return () => clearTimeout(timer);
  }, []);

  if (!mounted) return null;

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* PIĘKNE TŁO Z BALI - PEŁNOEKRANOWE */}
      <div className="absolute inset-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="Spektakularny zachód słońca na Bali - Retreaty Jogi"
          fill
          className="object-cover object-center scale-110 transition-transform duration-[30s] ease-out hover:scale-105"
          priority
          quality={95}
          sizes="100vw"
          onLoad={() => setImageLoaded(true)}
          onError={(e) => console.error('Błąd ładowania obrazu Hero:', e)}
        />

        {/* PIĘKNY GRADIENT OVERLAY - ZACHÓD SŁOŃCA */}
        <div className="absolute inset-0 bg-gradient-to-b from-orange-900/20 via-transparent to-purple-900/30"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/20"></div>

        {/* DODATKOWY OVERLAY DLA LEPSZEJ CZYTELNOŚCI */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>

        {/* Loading indicator z piękną animacją */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-gradient-to-br from-orange-200 via-pink-200 to-purple-200 flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-sage border-t-transparent rounded-full animate-spin mb-4 mx-auto"></div>
              <div className="text-sage text-xl font-light">Ładowanie magii Bali...</div>
            </div>
          </div>
        )}
      </div>

      {/* SPEKTAKULARNA ZAWARTOŚĆ NA TLE BALI */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        <div className={`text-center max-w-6xl mx-auto px-6 sm:px-8 lg:px-16 animate-float-gentle transition-all duration-1000 ${contentVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>

          {/* SUBTELNY BADGE */}
          <div className="inline-flex items-center px-6 py-2 mb-8 bg-white/10 backdrop-blur-md border border-white/20 text-white/90 text-sm font-light tracking-[3px] uppercase">
            Retreaty Jogi • Bali 2025
          </div>

          {/* SPEKTAKULARNY NAGŁÓWEK */}
          <h1 className="font-display mb-10 leading-[0.9] tracking-[-2px]">
            <div className="text-[64px] md:text-[80px] lg:text-[96px] font-light text-white mb-4 drop-shadow-2xl">
              Transformacyjne
            </div>
            <div className="text-[72px] md:text-[88px] lg:text-[104px] font-normal text-white mb-4 drop-shadow-2xl">
              Retreaty Jogi
            </div>
            <div className="text-[48px] md:text-[56px] lg:text-[64px] font-light drop-shadow-2xl" style={{ color: '#D4B5A0' }}>
              na magicznej Bali
            </div>
          </h1>

          {/* INSPIRUJĄCY PODTYTUŁ */}
          <p className="text-xl md:text-2xl text-white/95 mb-16 font-light max-w-4xl mx-auto leading-[1.6] tracking-wide drop-shadow-lg">
            Odkryj wewnętrzny spokój w tropikalnym raju, gdzie każdy wschód słońca<br className="hidden md:block" />
            przynosi nową możliwość transformacji ciała, umysłu i ducha
          </p>

          {/* SPEKTAKULARNE PRZYCISKI */}
          <div className="flex flex-col sm:flex-row gap-8 justify-center mb-20">
            {/* Przycisk główny - elegancki biały */}
            <Link
              href="/program"
              className="group inline-flex items-center justify-center px-12 py-5 bg-white text-dark-gray text-sm font-medium tracking-[2px] uppercase transition-all duration-500 hover:bg-sage hover:text-white hover:scale-105 hover:shadow-2xl"
            >
              <span>Zobacz Terminy 2025</span>
              <div className="ml-3 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1">→</div>
            </Link>

            {/* Przycisk drugi - elegancki transparentny */}
            <Link
              href="/zasoby"
              className="group inline-flex items-center justify-center px-12 py-5 bg-white/10 backdrop-blur-md border-2 border-white/30 text-white text-sm font-medium tracking-[2px] uppercase transition-all duration-500 hover:bg-white/20 hover:border-white/50 hover:scale-105"
            >
              <span>Pobierz Program</span>
              <div className="ml-3 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1">↓</div>
            </Link>
          </div>

          {/* ELEGANCKIE IKONY Z INFORMACJAMI */}
          <div className="flex flex-wrap justify-center gap-16 text-white/90">
            <div className="flex flex-col items-center gap-3 group">
              <div className="w-12 h-12 bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/20">
                <Calendar className="w-6 h-6" strokeWidth={1.5} />
              </div>
              <div className="text-center">
                <div className="font-medium text-sm tracking-wide">12 dni</div>
                <div className="font-light text-xs text-white/70 tracking-wider">transformacji</div>
              </div>
            </div>

            <div className="flex flex-col items-center gap-3 group">
              <div className="w-12 h-12 bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/20">
                <Users className="w-6 h-6" strokeWidth={1.5} />
              </div>
              <div className="text-center">
                <div className="font-medium text-sm tracking-wide">Max 12 osób</div>
                <div className="font-light text-xs text-white/70 tracking-wider">intymna grupa</div>
              </div>
            </div>

            <div className="flex flex-col items-center gap-3 group">
              <div className="w-12 h-12 bg-white/10 backdrop-blur-md border border-white/20 flex items-center justify-center transition-all duration-300 group-hover:bg-white/20">
                <Star className="w-6 h-6" strokeWidth={1.5} />
              </div>
              <div className="text-center">
                <div className="font-medium text-sm tracking-wide">All Inclusive</div>
                <div className="font-light text-xs text-white/70 tracking-wider">pełen komfort</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* SUBTELNY SCROLL INDICATOR */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex flex-col items-center text-white/60 animate-bounce">
          <div className="text-xs font-light tracking-[2px] uppercase mb-2">Przewiń w dół</div>
          <div className="w-6 h-10 border-2 border-white/30 flex justify-center">
            <div className="w-1 h-3 bg-white/50 mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>

    </section>
  );
};

export default RetreatHero;