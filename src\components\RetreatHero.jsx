'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, Users, Star } from 'lucide-react';

const RetreatHero = () => {
  const [mounted, setMounted] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <section className="relative h-[85vh] md:h-[90vh] overflow-hidden">
      {/* Tło z wysokiej jakości zdjęciem Bali */}
      <div className="absolute inset-0">
        <Image
          src="/images/background/bali-hero.webp"
          alt="Transformacyjne Retreaty Jogi na Bali - Piękny zachód słońca na Bali"
          fill
          className="object-cover"
          priority
          quality={90}
          sizes="100vw"
          onLoad={() => setImageLoaded(true)}
          onError={(e) => console.error('Błąd ładowania obrazu Hero:', e)}
        />
        {/* NOWY ELEGANCKI OVERLAY */}
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-black/10 to-black/30"></div>

        {/* Loading indicator */}
        {!imageLoaded && (
          <div className="absolute inset-0 bg-sage/20 flex items-center justify-center">
            <div className="text-primary text-lg">Ładowanie pięknego zachodu słońca na Bali...</div>
          </div>
        )}
      </div>

      {/* Centralna treść */}
      <div className="relative z-10 flex items-center justify-center h-full">
        <div className="text-center max-w-5xl mx-auto px-6 sm:px-8 lg:px-12">
          {/* NOWY NAGŁÓWEK - PLAYFAIR DISPLAY */}
          <h1 className="font-display mb-8 leading-[1.1] tracking-[-1px]">
            <div className="text-[56px] md:text-[64px] font-normal text-primary mb-2">
              Transformacyjne Retreaty
            </div>
            <div className="text-[64px] md:text-[72px] font-medium" style={{ color: '#D4B5A0' }}>
              Jogi na Bali
            </div>
          </h1>

          {/* NOWY PODTYTUŁ - 18px, maksymalnie 2 linie */}
          <p className="text-lg text-primary/90 mb-12 font-body font-light max-w-2xl mx-auto leading-[1.8] tracking-wide">
            Odkryj wewnętrzny spokój w tropikalnym raju.<br />
            Harmonia ciała i ducha czeka na Ciebie.
          </p>

          {/* NOWE ELEGANCKIE PRZYCISKI - PROSTOKĄTNE */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            {/* Przycisk główny - biały */}
            <Link
              href="/program"
              className="hero-btn-primary"
            >
              Zobacz Terminy 2025
            </Link>

            {/* Przycisk drugi - transparentny z borderem */}
            <Link
              href="/zasoby"
              className="hero-btn-secondary"
            >
              Pobierz Program
            </Link>
          </div>

          {/* MINIMALISTYCZNE IKONY - CIENKIE LINIE */}
          <div className="flex flex-wrap justify-center gap-12 text-primary/80">
            <div className="flex items-center gap-3">
              <Calendar className="w-5 h-5 text-primary/70" strokeWidth={1} />
              <span className="font-body font-light text-sm tracking-wide">12 dni transformacji</span>
            </div>

            <div className="flex items-center gap-3">
              <Users className="w-5 h-5 text-primary/70" strokeWidth={1} />
              <span className="font-body font-light text-sm tracking-wide">Maksymalnie 12 osób</span>
            </div>

            <div className="flex items-center gap-3">
              <Star className="w-5 h-5 text-primary/70" strokeWidth={1} />
              <span className="font-body font-light text-sm tracking-wide">All Inclusive</span>
            </div>
          </div>
        </div>
      </div>


    </section>
  );
};

export default RetreatHero;