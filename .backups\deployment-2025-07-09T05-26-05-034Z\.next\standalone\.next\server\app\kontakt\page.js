(()=>{var e={};e.id=9937,e.ids=[9937],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33470:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),o=s(88170),i=s.n(o),n=s(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let d={children:["",{children:["kontakt",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,34543)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\kontakt\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(s.bind(s,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\kontakt\\page.jsx"],c={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/kontakt/page",pathname:"/kontakt",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34543:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(37413),r=s(61120),o=s(68104),i=s(60343);function n(){return(0,a.jsx)("main",{className:"relative section-bg min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24",children:[(0,a.jsxs)("header",{className:"text-center mb-16",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center p-3 bg-temple/10 rounded-full mb-6",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-temple/70"})}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-serif text-temple tracking-tight mb-6 font-light",children:"Kontakt"}),(0,a.jsx)("div",{className:"decorative-line"}),(0,a.jsx)("p",{className:"text-lg text-wood-light/80 max-w-3xl mx-auto font-light",children:"Masz pytania lub chcesz zarezerwować miejsce? Skontaktuj się z nami!"})]}),(0,a.jsx)(r.Suspense,{fallback:(0,a.jsx)("div",{className:"flex items-center justify-center h-[40vh] text-wood-light",children:"Ładowanie formularza..."}),children:(0,a.jsx)(o.default,{})})]})})}},51873:(e,t,s)=>{Promise.resolve().then(s.bind(s,68104))},59965:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var a=s(60687),r=s(43210),o=s(2489),i=s(19526),n=s(66232),l=s(27900);function d(){let[e,t]=(0,r.useState)({name:"",email:"",message:"",honeypot:""}),[s,d]=(0,r.useState)(""),[m,c]=(0,r.useState)(!1),p=s=>{t({...e,[s.target.id]:s.target.value})},u=async s=>{if(s.preventDefault(),!e.honeypot){c(!0),d("Wysyłanie...");try{let s=await fetch("https://api.web3forms.com/submit",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({access_key:"YOUR_WEB3FORMS_ACCESS_KEY",name:e.name,email:e.email,message:e.message,subject:`Nowa wiadomość z Bali Yoga Journey od ${e.name}`,from_name:"Bali Yoga Journey",to_email:"<EMAIL>"})});if((await s.json()).success)d("✅ Wiadomość wysłana! Dziękujemy, odpowiemy wkr\xf3tce."),t({name:"",email:"",message:"",honeypot:""});else throw Error("Błąd wysyłania")}catch(e){console.error("Error:",e),d("❌ Wystąpił błąd. Spr\xf3buj ponownie lub napisz bezpoś<NAME_EMAIL>")}finally{c(!1),setTimeout(()=>d(""),8e3)}}},x=[{href:"https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr",label:"@fly_with_bakasana",icon:n.A,aria:"Profil na Instagramie"},{href:"https://www.facebook.com/p/Fly-with-bakasana-100077568306563/",label:"Fly with bakasana",icon:i.A,aria:"Profil na Facebooku"},{href:"https://app.fitssey.com/Flywithbakasana/frontoffice",label:"Rezerwacje Fitssey",icon:o.A,aria:"Profil na Fitssey (rezerwacje)"}];return(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-16 items-start",children:[(0,a.jsxs)("div",{className:"unified-card p-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-serif text-temple mb-6 font-light",children:"Napisz do nas"}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-wood-light mb-2",children:"Imię"}),(0,a.jsx)("input",{type:"text",id:"name",value:e.name,onChange:p,required:!0,className:"w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-full focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-wood-light mb-2",children:"Email"}),(0,a.jsx)("input",{type:"email",id:"email",value:e.email,onChange:p,required:!0,className:"w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-full focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-wood-light mb-2",children:"Wiadomość"}),(0,a.jsx)("textarea",{id:"message",value:e.message,onChange:p,required:!0,rows:5,className:"w-full px-4 py-3 bg-rice/50 border border-bamboo/20 rounded-lg focus:border-temple/50 focus:ring-2 focus:ring-temple/20 focus:outline-none transition-colors text-temple resize-none"})]}),(0,a.jsx)("input",{type:"text",id:"honeypot",name:"honeypot",value:e.honeypot,onChange:p,style:{display:"none"},tabIndex:"-1",autoComplete:"off"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("button",{type:"submit",disabled:m,className:`btn-unified-primary group ${m?"opacity-50 cursor-not-allowed":""}`,children:[m?"Wysyłanie...":"Wyślij Wiadomość",(0,a.jsx)(l.A,{className:`ml-2 h-4 w-4 transition-transform ${m?"":"group-hover:translate-x-1"}`})]}),s&&(0,a.jsx)("p",{className:"text-sm text-temple font-medium max-w-xs",children:s})]})]})]}),(0,a.jsxs)("div",{className:"unified-card p-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-serif text-temple mb-4 font-light",children:"Połączmy siły!"}),(0,a.jsx)("p",{className:"text-wood-light mb-8 leading-relaxed font-light",children:"Masz pytania, sugestie, a może chcesz po prostu porozmawiać o jodze i Bali? Czekam na Twoją wiadomość! Znajdziesz nas r\xf3wnież w mediach społecznościowych i na platformie Fitssey."}),(0,a.jsx)("h4",{className:"text-xl font-serif text-temple mb-5 font-light",children:"Znajdź nas na:"}),(0,a.jsx)("div",{className:"space-y-4",children:x.map(e=>{let t=e.icon;return(0,a.jsxs)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer","aria-label":e.aria,className:"flex items-center gap-3 p-3 rounded-lg transition-colors duration-300 group hover:bg-temple/5",children:[(0,a.jsx)(t,{className:"w-6 h-6 text-temple/80 group-hover:text-temple transition-colors"}),(0,a.jsx)("span",{className:"text-base text-wood-light group-hover:text-temple transition-colors",children:e.label})]},e.label)})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68104:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\kontakt\\ContactForm.jsx","default")},88321:(e,t,s)=>{Promise.resolve().then(s.bind(s,59965))}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>s(33470));module.exports=a})();