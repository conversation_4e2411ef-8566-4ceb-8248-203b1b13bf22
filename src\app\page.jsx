import React from 'react';
import Link from 'next/link';
import RetreatHero from '@/components/RetreatHero';
import TrustBadges from '@/components/TrustBadges';

export default function HomePage() {

  return (
    <div className="relative">
      <RetreatHero />

      {/* Trust Badges - dodane zaraz po hero */}
      <TrustBadges />

      {/* Sekcja wprowadzająca - NOWE WYMIARY SPA */}
      <section className="py-32 relative bg-off-white">
        <div className="max-w-[1140px] mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-[42px] font-display font-normal text-dark-gray max-w-4xl mx-auto mb-16 leading-tight">
              Gotowa na przygodę życia?
            </h2>

            <div className="flex items-center justify-center mb-16">
              <div className="h-px w-24 bg-gradient-to-r from-transparent via-sage to-transparent opacity-30" />
            </div>

            <p className="font-body text-[17px] max-w-[700px] mx-auto leading-[1.8] text-text-gray mb-16">
              Dołącz do naszego najbliższego retreatu jogowego na Bali i odkryj harmonię ciała i ducha w magicznym otoczeniu wyspy bogów.
            </p>

            <div className="text-sm font-body text-text-gray/60 mb-12 tracking-wide font-light">
              Bali • Sri Lanka • 2025 • Transformacja
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-8 justify-center max-w-3xl mx-auto">
            <Link
              href="/program"
              className="btn-primary"
              aria-label="Zobacz retreaty"
            >
              Zobacz retreaty
            </Link>
            <Link
              href="/kontakt"
              className="btn-secondary"
              aria-label="Skontaktuj się"
            >
              Skontaktuj się
            </Link>
          </div>
        </div>
      </section>

      {/* WhatsApp Button - SZAŁWIOWY ELEGANCKI */}
      <a
        href="https://wa.me/48606101523?text=Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-8 right-8 text-primary px-6 py-4 font-body font-normal text-sm tracking-[1px] transition-all duration-300 hover:-translate-y-1 z-50"
        aria-label="Skontaktuj się przez WhatsApp"
        style={{
          backgroundColor: '#7C9885',
          boxShadow: '0 5px 25px rgba(124,152,133,0.3)'
        }}
      >
        NAPISZ DO NAS
      </a>
    </div>
  );
}
