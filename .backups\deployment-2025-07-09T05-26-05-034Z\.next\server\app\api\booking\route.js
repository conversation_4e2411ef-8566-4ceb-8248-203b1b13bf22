"use strict";(()=>{var e={};e.id=3103,e.ids=[3103],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69110:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>d,POST:()=>p});var n=r(96559),o=r(48088),i=r(37719),s=r(32190);let c=new Map;async function p(e){try{var t,r,a;let n=function(e){let t=e.headers.get("x-forwarded-for"),r=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():r||"unknown"}(e);if(function(e){let t=Date.now(),r=(c.get(e)||[]).filter(e=>t-e<9e5);return r.length>=3||(r.push(t),c.set(e,r),!1)}(n))return console.warn(`Rate limit exceeded for booking API from IP: ${n}`),s.NextResponse.json({success:!1,error:"Zbyt wiele pr\xf3b rezerwacji. Spr\xf3buj ponownie za 15 minut.",rateLimitExceeded:!0},{status:429});let{retreat:o,formData:i,timestamp:p}=await e.json();if(!o||!i)return s.NextResponse.json({success:!1,error:"Missing required data"},{status:400});for(let e of["firstName","lastName","email","phone","emergencyContact","emergencyPhone"])if(!i[e]?.trim())return s.NextResponse.json({success:!1,error:`Missing required field: ${e}`},{status:400});if(!i.email.includes("@"))return s.NextResponse.json({success:!1,error:"Invalid email format"},{status:400});let d=o.price||0,l=500*("single"===i.roomPreference),m=d+l,g=Math.round(.3*m),w=`BR${Date.now().toString().slice(-6)}${Math.random().toString(36).substr(2,3).toUpperCase()}`,f={bookingReference:w,retreat:{id:o.id,title:o.title,startDate:o.start,endDate:o.end,basePrice:d},customer:{firstName:i.firstName,lastName:i.lastName,email:i.email,phone:i.phone,birthDate:i.birthDate,emergencyContact:i.emergencyContact,emergencyPhone:i.emergencyPhone},preferences:{yogaExperience:i.yogaExperience,dietaryRestrictions:i.dietaryRestrictions,medicalConditions:i.medicalConditions,roomPreference:i.roomPreference,specialRequests:i.specialRequests},pricing:{basePrice:d,roomSupplement:l,totalPrice:m,depositAmount:g,remainingAmount:m-g},payment:{method:i.paymentMethod,depositPaid:!1,fullPaymentPaid:!1},agreements:{termsAccepted:i.agreeTerms,newsletterOptIn:i.agreeNewsletter},status:"pending_deposit",createdAt:p||new Date().toISOString()};if(await u(f),i.agreeNewsletter)try{await fetch(`${process.env.NEXTAUTH_URL||"http://localhost:3000"}/api/newsletter`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:i.email,tags:["retreat-booking","customer"],source:"booking-form",firstName:i.firstName,lastName:i.lastName})})}catch(e){console.warn("Newsletter signup failed:",e)}try{await fetch(`${process.env.NEXTAUTH_URL||"http://localhost:3000"}/api/email-sequences`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sequenceType:"booking_confirmation",recipientEmail:i.email,recipientName:`${i.firstName} ${i.lastName}`,customData:{bookingReference:w,retreatTitle:o.title,totalPrice:m,depositAmount:g,paymentMethod:i.paymentMethod},triggerImmediately:!0})})}catch(e){console.warn("Email sequence failed:",e)}return s.NextResponse.json({success:!0,message:"Booking created successfully",booking:{reference:w,totalPrice:m,depositAmount:g,paymentInstructions:(t=i.paymentMethod,r=g,a=w,"transfer"===t?{type:"bank_transfer",amount:r,reference:a,instructions:`
        Dane do przelewu:
        
        Odbiorca: Julia Jakubowicz
        Numer konta: 12 3456 7890 1234 5678 9012 3456
        Kwota: ${r} PLN
        Tytuł: Zadatek retreat ${a}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
        Resztę kwoty (${2.33*r} PLN) wpłać 30 dni przed wyjazdem.
      `}:"blik"===t?{type:"blik",amount:r,reference:a,instructions:`
        Link do płatności BLIK zostanie wysłany na Tw\xf3j email w ciągu 15 minut.
        
        Kwota zadatku: ${r} PLN
        Numer rezerwacji: ${a}
        
        Po wpłacie zadatku Twoja rezerwacja zostanie potwierdzona.
      `}:{type:"contact",instructions:"Skontaktuj się z nami w sprawie płatności: <EMAIL>"})}})}catch(e){return console.error("Booking error:",e),s.NextResponse.json({success:!1,error:"Failed to process booking",details:void 0},{status:500})}}async function u(e){return e.bookingReference,e.retreat.title,e.pricing.totalPrice,e.pricing.depositAmount,await new Promise(e=>setTimeout(e,1e3)),!0}async function d(){return s.NextResponse.json({message:"Booking API is working",timestamp:new Date().toISOString()})}let l=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/booking/route",pathname:"/api/booking",filename:"route",bundlePath:"app/api/booking/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\api\\booking\\route.js",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:w}=l;function f(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>r(69110));module.exports=a})();