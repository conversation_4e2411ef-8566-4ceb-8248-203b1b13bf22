(()=>{var e={};e.id=5477,e.ids=[5477],e.modules={157:(e,t,s)=>{"use strict";s.d(t,{bookingFAQs:()=>r,default:()=>i});var a=s(12907);let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\components\\\\FAQSection.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\components\\FAQSection.jsx","default");(0,a.registerClientReference)(function(){throw Error("Attempted to call CompactFAQ() from the server but CompactFAQ is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\components\\FAQSection.jsx","CompactFAQ"),(0,a.registerClientReference)(function(){throw Error("Attempted to call retreatFAQs() from the server but retreatFAQs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\components\\FAQSection.jsx","retreatFAQs");let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call bookingFAQs() from the server but bookingFAQs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\components\\FAQSection.jsx","bookingFAQs")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8738:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),i=s(48088),r=s(88170),n=s.n(r),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["rezerwacja",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14663)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\rezerwacja\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(s.bind(s,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\rezerwacja\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/rezerwacja/page",pathname:"/rezerwacja",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14663:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var a=s(37413);s(61120);var i=s(75124),r=s(96441),n=s(157);let l=(0,i.X2)({title:"Rezerwacja Retreatu - Kalendarz Dostępności",description:"Sprawdź dostępne terminy retreat\xf3w jogowych na Bali i Sri Lance. Zarezerwuj swoje miejsce na niezapomnianej przygodzie.",keywords:["rezerwacja retreat","kalendarz","dostępność","booking","retreat jogowy Bali"]});function o(){return(0,a.jsx)("main",{className:"py-20 min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto container-padding",children:[(0,a.jsxs)("header",{className:"text-center mb-16",children:[(0,a.jsxs)("h1",{className:"text-4xl md:text-5xl font-serif text-temple mb-6 font-light",children:["Rezerwacja ",(0,a.jsx)("span",{className:"gradient-text",children:"Retreatu"})]}),(0,a.jsx)("p",{className:"text-lg text-wood-light max-w-3xl mx-auto leading-relaxed mb-8 font-light",children:"Wybierz termin, kt\xf3ry Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej przygodzie jogowej na Bali lub Sri Lance."}),(0,a.jsx)("div",{className:"decorative-line"})]}),(0,a.jsx)(r.default,{}),(0,a.jsxs)("section",{className:"mt-20 grid md:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"bg-temple/5 p-8 rounded-2xl",children:[(0,a.jsx)("h3",{className:"text-xl font-serif text-temple mb-4",children:"Jak przebiega rezerwacja?"}),(0,a.jsxs)("div",{className:"space-y-4 text-wood-light",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5",children:"1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Wybierz termin"})," - Kliknij na wybrany retreat w kalendarzu"]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5",children:"2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Wypełnij formularz"})," - Podaj dane osobowe i preferencje"]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5",children:"3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Wpłać zadatek"})," - 30% wartości retreatu (dane w emailu)"]})]}),(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("span",{className:"bg-temple text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-medium mt-0.5",children:"4"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"Otrzymaj potwierdzenie"})," - Email z wszystkimi szczeg\xf3łami"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-golden/5 p-8 rounded-2xl",children:[(0,a.jsx)("h3",{className:"text-xl font-serif text-temple mb-4",children:"Co jest wliczone w cenę?"}),(0,a.jsxs)("div",{className:"space-y-3 text-wood-light",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"7 nocy w hotelu (pok\xf3j dzielony)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Wszystkie posiłki (śniadanie, lunch, kolacja)"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Transport lotnisko-hotel-lotnisko"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Praktyka jogi 2x dziennie"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Medytacje i warsztaty"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Zwiedzanie i wycieczki"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("span",{className:"text-temple",children:"✓"}),(0,a.jsx)("span",{children:"Opieka instruktora przez cały pobyt"})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-temple/10",children:[(0,a.jsx)("h4",{className:"font-medium text-temple mb-2",children:"Dodatkowo płatne:"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-wood-light",children:[(0,a.jsx)("div",{children:"• Pok\xf3j jednoosobowy: +500 PLN"}),(0,a.jsx)("div",{children:"• Bilety lotnicze (pomoc w organizacji)"}),(0,a.jsx)("div",{children:"• Ubezpieczenie podr\xf3żne"}),(0,a.jsx)("div",{children:"• Wydatki osobiste i pamiątki"})]})]})]})]}),(0,a.jsx)(n.default,{faqs:n.bookingFAQs,title:"Często zadawane pytania o rezerwację",subtitle:"Znajdź odpowiedzi na pytania dotyczące procesu rezerwacji i płatności",className:"mt-20"}),(0,a.jsx)("section",{className:"mt-20 text-center",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-temple/5 to-golden/5 rounded-2xl p-12",children:[(0,a.jsx)("h3",{className:"text-2xl font-serif text-temple mb-4",children:"Masz pytania?"}),(0,a.jsx)("p",{className:"text-wood-light mb-8 max-w-2xl mx-auto",children:"Jeśli nie znalazłeś odpowiedzi na swoje pytanie lub potrzebujesz pomocy z rezerwacją, skontaktuj się z nami bezpośrednio."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"btn-unified-primary",children:"Napisz email"}),(0,a.jsx)("a",{href:"tel:+48606101523",className:"btn-unified-secondary",children:"Zadzwoń: +48 606 101 523"})]})]})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55289:(e,t,s)=>{"use strict";let a,i,r;s.d(t,{default:()=>x});var n=s(60687),l=s(43210),o=s(13508),d=s(90780);let c=({retreat:e,onClose:t})=>{let[s,a]=(0,l.useState)({firstName:"",lastName:"",email:"",phone:"",preferredDate:"",notes:""}),[i,r]=(0,l.useState)(!1),[d,c]=(0,l.useState)(!1),m=e=>{let{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))},p=async a=>{a.preventDefault(),r(!0);try{let a=await fetch("/api/waiting-list",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({retreat:{id:e.id,title:e.title,startDate:e.start,endDate:e.end},customer:s,timestamp:new Date().toISOString()})});(await a.json()).success?(c(!0),setTimeout(()=>{t()},3e3)):alert("Wystąpił błąd. Spr\xf3buj ponownie lub skontaktuj się z nami.")}catch(e){console.error("Waiting list error:",e),alert("Wystąpił błąd. Spr\xf3buj ponownie lub skontaktuj się z nami.")}finally{r(!1)}};return d?(0,n.jsxs)(o.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"text-center p-8",children:[(0,n.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE4F"}),(0,n.jsx)("h3",{className:"text-2xl font-serif text-temple mb-4",children:"Dziękujemy!"}),(0,n.jsxs)("p",{className:"text-wood-light mb-4",children:["Zostałeś/aś dodany/a do listy oczekujących na retreat ",(0,n.jsx)("strong",{children:e.title}),"."]}),(0,n.jsx)("p",{className:"text-sm text-wood-light",children:"Skontaktujemy się z Tobą, gdy tylko zwolni się miejsce lub pojawi się nowy termin."}),(0,n.jsx)("div",{className:"mt-6",children:(0,n.jsx)("div",{className:"bg-temple/5 p-4 rounded-lg",children:(0,n.jsxs)("p",{className:"text-sm text-temple",children:["\uD83D\uDCA1 ",(0,n.jsx)("strong",{children:"Wskaz\xf3wka:"})," Sprawdź inne dostępne terminy lub zapisz się na newsletter, aby być na bieżąco z nowymi datami retreat\xf3w."]})})})]}):(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("h3",{className:"text-2xl font-serif text-temple mb-2",children:"Lista oczekujących"}),(0,n.jsxs)("p",{className:"text-wood-light",children:["Retreat ",(0,n.jsx)("strong",{children:e.title})," jest wyprzedany. Dołącz do listy oczekujących - powiadomimy Cię gdy zwolni się miejsce!"]})]}),(0,n.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Imię *"}),(0,n.jsx)("input",{type:"text",name:"firstName",value:s.firstName,onChange:m,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent",placeholder:"Twoje imię"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Nazwisko *"}),(0,n.jsx)("input",{type:"text",name:"lastName",value:s.lastName,onChange:m,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent",placeholder:"Twoje nazwisko"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Email *"}),(0,n.jsx)("input",{type:"email",name:"email",value:s.email,onChange:m,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent",placeholder:"<EMAIL>"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Telefon"}),(0,n.jsx)("input",{type:"tel",name:"phone",value:s.phone,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent",placeholder:"+48 123 456 789"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Preferowany termin alternatywny"}),(0,n.jsxs)("select",{name:"preferredDate",value:s.preferredDate,onChange:m,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent",children:[(0,n.jsx)("option",{value:"",children:"Wybierz preferowany okres"}),(0,n.jsx)("option",{value:"spring",children:"Wiosna (marzec-maj)"}),(0,n.jsx)("option",{value:"summer",children:"Lato (czerwiec-sierpień)"}),(0,n.jsx)("option",{value:"autumn",children:"Jesień (wrzesień-listopad)"}),(0,n.jsx)("option",{value:"winter",children:"Zima (grudzień-luty)"}),(0,n.jsx)("option",{value:"any",children:"Dowolny termin"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-temple mb-2",children:"Dodatkowe uwagi"}),(0,n.jsx)("textarea",{name:"notes",value:s.notes,onChange:m,rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent resize-none",placeholder:"Czy masz preferencje co do lokalizacji, typu retreatu itp.?"})]}),(0,n.jsxs)("div",{className:"bg-golden/10 p-4 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-temple mb-2",children:"Korzyści z listy oczekujących:"}),(0,n.jsxs)("ul",{className:"text-sm text-wood-light space-y-1",children:[(0,n.jsx)("li",{children:"✨ Pierwszeństwo przy rezerwacji zwolnionych miejsc"}),(0,n.jsx)("li",{children:"\uD83C\uDFAF Powiadomienie o nowych terminach tego retreatu"}),(0,n.jsx)("li",{children:"\uD83D\uDC8E Specjalne zniżki dla os\xf3b z listy oczekujących"}),(0,n.jsx)("li",{children:"\uD83D\uDCE7 Newsletter z wyjątkowymi ofertami"})]})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-4",children:[(0,n.jsx)("button",{type:"submit",disabled:i,className:"btn-unified-primary flex-1 disabled:opacity-50 disabled:cursor-not-allowed",children:i?(0,n.jsxs)("span",{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Dodawanie..."]}):"Dołącz do listy oczekujących"}),(0,n.jsx)("button",{type:"button",onClick:t,className:"btn-unified-secondary flex-1",children:"Anuluj"})]})]}),(0,n.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[(0,n.jsx)("h4",{className:"font-medium text-temple mb-3",children:"Inne opcje:"}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,n.jsx)("a",{href:"/program",className:"btn-unified-outline text-sm",onClick:t,children:"Zobacz inne retreaty"}),(0,n.jsx)("a",{href:"/kontakt",className:"btn-unified-outline text-sm",onClick:t,children:"Skontaktuj się z nami"})]})]})]})};var m=s(8958);let p=[{id:1,title:"Bali Yoga Retreat",start:new Date(2025,2,15),end:new Date(2025,2,22),price:2900,spotsLeft:3,status:"available",description:"Wiosenny retreat jogowy na Bali z praktyką na plażach Uluwatu",includes:["7 nocy w hotelu","Wszystkie posiłki","Transport","Joga 2x dziennie","Zwiedzanie"]},{id:2,title:"Bali Yoga Retreat",start:new Date(2025,4,10),end:new Date(2025,4,17),price:3200,spotsLeft:8,status:"available",description:"Majowy retreat z fokusem na advanced asany i medytację",includes:["7 nocy w hotelu","Wszystkie posiłki","Transport","Joga 2x dziennie","Warsztaty"]},{id:3,title:"Bali Yoga Retreat",start:new Date(2025,6,5),end:new Date(2025,6,12),price:3400,spotsLeft:0,status:"full",description:"Letni retreat - WYPRZEDANY",includes:["7 nocy w hotelu","Wszystkie posiłki","Transport","Joga 2x dziennie"]},{id:4,title:"Sri Lanka Yoga Retreat",start:new Date(2025,8,20),end:new Date(2025,8,27),price:2700,spotsLeft:12,status:"available",description:"Jesienny retreat na Sri Lance - Ella i Sigiriya",includes:["7 nocy w hotelach","Wszystkie posiłki","Transport","Joga w g\xf3rach","Safari"]}];function x(){let[e,t]=(0,l.useState)(null),[s,x]=(0,l.useState)(!1),[u,h]=(0,l.useState)(!1),[j,y]=(0,l.useState)("month"),[w,z]=(0,l.useState)(!1),[f,g]=(0,l.useState)("blik");return(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)(d.ScrollReveal,{className:"text-center",children:[(0,n.jsx)("h2",{className:"text-3xl md:text-4xl font-serif text-temple mb-4",children:"Kalendarz Retreat\xf3w"}),(0,n.jsx)("p",{className:"text-wood-light text-lg max-w-2xl mx-auto",children:"Wybierz termin, kt\xf3ry Ci odpowiada i zarezerwuj swoje miejsce na niezapomnianej przygodzie"})]}),(0,n.jsxs)("div",{className:"flex flex-wrap justify-center gap-4 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-4 h-4 bg-temple rounded"}),(0,n.jsx)("span",{children:"Dostępne miejsca"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-4 h-4 bg-amber-500 rounded"}),(0,n.jsx)("span",{children:"Ostatnie miejsca"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-4 h-4 bg-gray-400 rounded"}),(0,n.jsx)("span",{children:"Wyprzedane"})]})]}),(0,n.jsx)(d.ScrollReveal,{className:"bg-white rounded-2xl shadow-soft p-6 overflow-hidden",children:(0,n.jsx)("div",{style:{height:"600px"},children:w&&a&&i?(0,n.jsx)(a,{localizer:i(r),events:p,startAccessor:"start",endAccessor:"end",style:{height:"100%"},eventPropGetter:e=>{let t="#8B7355",s="#8B7355";return"full"===e.status?(t="#9CA3AF",s="#9CA3AF"):e.spotsLeft<=3&&(t="#F59E0B",s="#F59E0B"),{style:{backgroundColor:t,borderColor:s,color:"white",border:"none",borderRadius:"6px",fontSize:"12px",fontWeight:"500"}}},messages:{allDay:"Cały dzień",previous:"Poprzedni",next:"Następny",today:"Dziś",month:"Miesiąc",week:"Tydzień",day:"Dzień",agenda:"Agenda",date:"Data",time:"Czas",event:"Wydarzenie",noEventsInRange:"Brak retreat\xf3w w tym okresie",showMore:e=>`+ ${e} więcej`},views:["month","agenda"],view:j,onView:y,onSelectEvent:e=>{t(e)},popup:!0,showMultiDayTimes:!0,step:60,showAllEvents:!0,className:"custom-calendar"}):(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC5"}),(0,n.jsx)("h3",{className:"text-xl font-serif text-temple mb-2",children:"Kalendarz się ładuje..."}),(0,n.jsx)("p",{className:"text-wood-light",children:"Proszę czekać"})]})})})}),(0,n.jsx)(o.N,{children:e&&(0,n.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:()=>t(null),children:(0,n.jsxs)(o.P.div,{initial:{scale:.9,y:50},animate:{scale:1,y:0},exit:{scale:.9,y:50},className:"bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("h3",{className:"text-2xl font-serif text-temple mb-2",children:e.title}),(0,n.jsxs)("p",{className:"text-wood-light mb-4",children:[r(e.start).format("DD MMMM")," - ",r(e.end).format("DD MMMM YYYY")]}),(0,n.jsxs)("div",{className:"flex justify-center items-center gap-4 mb-4",children:[(0,n.jsxs)("span",{className:"text-3xl font-bold text-temple",children:[e.price," PLN"]}),"available"===e.status&&(0,n.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm",children:[e.spotsLeft," miejsc dostępnych"]}),"full"===e.status&&(0,n.jsx)("span",{className:"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm",children:"Wyprzedane"})]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-temple mb-2",children:"Opis retreatu:"}),(0,n.jsx)("p",{className:"text-wood-light",children:e.description})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-temple mb-2",children:"W cenie:"}),(0,n.jsx)("ul",{className:"space-y-1",children:e.includes.map((e,t)=>(0,n.jsxs)("li",{className:"flex items-center gap-2 text-wood-light",children:[(0,n.jsx)("span",{className:"text-temple",children:"✓"}),e]},t))})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 pt-6",children:["available"===e.status?(0,n.jsx)("button",{onClick:()=>{"full"===e.status?h(!0):x(!0)},className:"btn-unified-primary flex-1",children:"Zarezerwuj miejsce"}):(0,n.jsx)("button",{onClick:()=>{h(!0)},className:"btn-unified-primary flex-1 bg-golden hover:bg-golden/90",children:"Dołącz do listy oczekujących"}),(0,n.jsx)("button",{onClick:()=>t(null),className:"btn-unified-secondary flex-1",children:"Zamknij"})]})]})]})})}),(0,n.jsx)(o.N,{children:u&&e&&(0,n.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:()=>h(!1),children:(0,n.jsx)(o.P.div,{initial:{scale:.9,y:50},animate:{scale:1,y:0},exit:{scale:.9,y:50},className:"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,n.jsx)(c,{retreat:e,onClose:()=>{h(!1),t(null)}})})})}),(0,n.jsx)(o.N,{children:s&&e&&(0,n.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",onClick:()=>x(!1),children:(0,n.jsx)(o.P.div,{initial:{scale:.9,y:50},animate:{scale:1,y:0},exit:{scale:.9,y:50},className:"bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsxs)("h3",{className:"text-2xl font-serif text-temple mb-2",children:["Rezerwacja: ",e.title]}),(0,n.jsxs)("p",{className:"text-wood-light",children:[r(e.start).format("DD MMMM")," - ",r(e.end).format("DD MMMM YYYY")]})]}),(0,n.jsx)(m.A,{selectedMethod:f,onPaymentMethodChange:g}),(0,n.jsx)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,n.jsx)("button",{onClick:()=>{alert(`Rezerwacja z płatnością: ${f}. Formularz zostanie wdrożony w następnej aktualizacji.`)},className:"btn-unified-primary flex-1",children:"Przejdź do formularza"}),(0,n.jsx)("button",{onClick:()=>x(!1),className:"btn-unified-secondary flex-1",children:"Anuluj"})]})})]})})})}),(0,n.jsxs)(d.ScrollReveal,{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"text-center p-6 bg-temple/5 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-temple mb-2",children:p.filter(e=>"available"===e.status).length}),(0,n.jsx)("div",{className:"text-wood-light",children:"Dostępne retreaty"})]}),(0,n.jsxs)("div",{className:"text-center p-6 bg-golden/5 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-temple mb-2",children:p.reduce((e,t)=>e+("available"===t.status?t.spotsLeft:0),0)}),(0,n.jsx)("div",{className:"text-wood-light",children:"Wolne miejsca"})]}),(0,n.jsxs)("div",{className:"text-center p-6 bg-sunset/5 rounded-xl",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-temple mb-2",children:"7-8"}),(0,n.jsx)("div",{className:"text-wood-light",children:"Dni retreatu"})]})]})]})}},55777:(e,t,s)=>{Promise.resolve().then(s.bind(s,96441)),Promise.resolve().then(s.bind(s,157))},56178:(e,t,s)=>{"use strict";s.d(t,{bookingFAQs:()=>d,default:()=>o});var a=s(60687),i=s(43210),r=s(13508),n=s(90780);let l=[{question:"Czy retreat jest odpowiedni dla początkujących?",answer:"Absolutnie! Nasze retreaty są dostosowane do wszystkich poziom\xf3w zaawansowania. Ważna jest chęć do nauki i otwartość na nowe doświadczenia, nie poziom umiejętności. Każdą praktykę dostosowujemy indywidualnie do możliwości uczestnik\xf3w."},{question:"Co jest wliczone w cenę retreatu?",answer:"W cenie retreatu masz wszystko: 7 nocy w hotelu (pok\xf3j dzielony), wszystkie posiłki (śniadanie, lunch, kolacja), transport lotnisko-hotel-lotnisko, praktykę jogi 2x dziennie, medytacje, warsztaty, zwiedzanie najpiękniejszych miejsc Bali oraz opiekę instruktora przez cały pobyt."},{question:"Jak wygląda typowy dzień na retreatie?",answer:"Dzień zaczyna się o 6:30 poranną praktyką jogi, następnie śniadanie o 8:00. O 10:00 wyruszamy zwiedzać magiczne miejsca Bali, lunch o 14:00, czas wolny, wieczorna praktyka o 17:00 i kolacja o 19:00. Każdy dzień jest inny i pełen niezapomnianych doświadczeń."},{question:"Czy mogę anulować rezerwację?",answer:"Tak, możesz anulować rezerwację do 30 dni przed wyjazdem z zwrotem 50% zadatku. Anulacja do 14 dni przed wyjazdem - zwrot 25% zadatku. Szczeg\xf3łowe warunki znajdziesz w regulaminie."},{question:"Jakie są warunki płatności?",answer:"Przy rezerwacji wpłacasz zadatek w wysokości 30% wartości retreatu. Resztę kwoty wpłacasz 30 dni przed wyjazdem. Akceptujemy przelewy bankowe i BLIK. Wszystkie szczeg\xf3ły otrzymasz w emailu potwierdzającym rezerwację."},{question:"Co powinienem zabrać ze sobą?",answer:"Matę do jogi (jeśli masz ulubioną), wygodne ubrania do praktyki, str\xf3j kąpielowy, krem z wysokim filtrem, kapelusz, lekkie ubrania na dzień i coś cieplejszego na wiecz\xf3r. Pełną listę rzeczy do spakowania otrzymasz po rezerwacji."},{question:"Czy jest możliwość przedłużenia pobytu?",answer:"Tak! Możemy pom\xf3c w organizacji dodatkowych dni na Bali przed lub po retreatie. Polecamy hotele, pomagamy z transferami i podpowiadamy, co warto zobaczyć. Skontaktuj się z nami po dokonaniu rezerwacji."},{question:"Jakie są wymagania zdrowotne?",answer:"Nie ma specjalnych wymagań zdrowotnych. Ważne jest, żebyś poinformowała nas o ewentualnych kontuzjach, problemach zdrowotnych lub przyjmowanych lekach. Dzięki temu będziemy mogli dostosować praktykę do Twoich potrzeb."}];function o({faqs:e=l,title:t="Często zadawane pytania",subtitle:s="Znajdź odpowiedzi na najczęściej zadawane pytania o nasze retreaty",className:o=""}){let[d,c]=(0,i.useState)(null),m=e=>{c(d===e?null:e)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map(e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}}))})}}),(0,a.jsx)("section",{className:`py-20 ${o}`,children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto container-padding",children:[(0,a.jsxs)(n.ScrollReveal,{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-serif text-temple mb-4",children:t}),(0,a.jsx)("p",{className:"text-wood-light text-lg max-w-2xl mx-auto",children:s})]}),(0,a.jsx)("div",{className:"space-y-4",children:e.map((e,t)=>(0,a.jsxs)(n.ScrollReveal,{delay:.1*t,className:"bg-white rounded-2xl shadow-soft overflow-hidden",children:[(0,a.jsxs)("button",{onClick:()=>m(t),className:"w-full px-8 py-6 text-left flex items-center justify-between hover:bg-shell/10 transition-colors","aria-expanded":d===t,"aria-controls":`faq-answer-${t}`,children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-temple pr-4",children:e.question}),(0,a.jsx)(r.P.div,{animate:{rotate:180*(d===t)},transition:{duration:.3},className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-6 h-6 text-temple",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),(0,a.jsx)(r.N,{children:d===t&&(0,a.jsx)(r.P.div,{id:`faq-answer-${t}`,initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:(0,a.jsx)("div",{className:"px-8 pb-6 text-wood-light leading-relaxed",children:e.answer})})})]},t))}),(0,a.jsx)(n.ScrollReveal,{className:"text-center mt-16",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-temple/5 to-golden/5 rounded-2xl p-8",children:[(0,a.jsx)("h3",{className:"text-xl font-serif text-temple mb-4",children:"Nie znalazłeś odpowiedzi?"}),(0,a.jsx)("p",{className:"text-wood-light mb-6",children:"Skontaktuj się z nami bezpośrednio - chętnie odpowiemy na wszystkie pytania!"}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"btn-unified-primary",children:"Napisz email"}),(0,a.jsx)("a",{href:"tel:+48606101523",className:"btn-unified-secondary",children:"Zadzwoń: +48 606 101 523"})]})]})})]})})]})}let d=[{question:"Jak długo mam na wpłacenie reszty kwoty?",answer:"Resztę kwoty (70% wartości retreatu) wpłacasz 30 dni przed wyjazdem. Otrzymasz przypomnienie emailem z dokładnymi instrukcjami."},{question:"Czy mogę zmienić termin rezerwacji?",answer:"Tak, możesz zmienić termin do 60 dni przed wyjazdem bez dodatkowych opłat (pod warunkiem dostępności miejsc w nowym terminie)."},{question:"Co w przypadku problem\xf3w z lotem?",answer:"Pomagamy w organizacji lot\xf3w i jesteśmy w kontakcie przed wyjazdem. W przypadku op\xf3źnień lub odwołań lot\xf3w, dostosowujemy transfer i program."}]},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68929:(e,t,s)=>{Promise.resolve().then(s.bind(s,55289)),Promise.resolve().then(s.bind(s,56178))},96441:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\components\\\\BookingCalendar.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\components\\BookingCalendar.jsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>s(8738));module.exports=a})();