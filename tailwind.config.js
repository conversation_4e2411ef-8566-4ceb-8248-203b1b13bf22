/** @type {import('tailwindcss').Config} */
export default {
  darkMode: ['class'],
  content: [
    './src/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,jsx,ts,tsx}',
    './src/components/**/*.{js,jsx,ts,tsx}',
    './src/pages/**/*.{js,jsx,ts,tsx}',
  ],
  corePlugins: {
    preflight: true,
  },
  theme: {
    extend: {
      colors: {
        // NOWA ELEGANCKA PALETA - SPA HOTEL 5*
        primary: '#FFFFFF',        // Biały
        'off-white': '#FAFAF8',    // Off-white
        sage: '#7C9885',           // Szałwiowy (główny akcent)
        sand: '#D4B5A0',           // <PERSON>ie<PERSON><PERSON>y piasek (tylko akcenty)
        'text-gray': '#666666',    // S<PERSON>y tekst
        'dark-gray': '#2C3E50',    // Ciemnoszary

        // Delika<PERSON>ne odcienie
        'sage-light': '#A8C4B2',   // Jaśniejszy szałwiowy
        'sage-dark': '#6B8A78',    // Ciemniejszy szałwiowy
        'sand-light': '#E5CDB8',   // Jaśniejszy piasek

        // Zachowujemy dla kompatybilności (będą stopniowo zastępowane)
        secondary: '#FAFAF8',
        accent: '#2C3E50',
        temple: '#2C3E50',
        rice: '#FFFFFF',
        lotus: '#FAFAF8',
        wood: '#666666',
        'warm-gold': '#D4B5A0',    // Zastąpione piaskowym
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        serif: ['Playfair Display', 'Georgia', 'serif'],
        display: ['Playfair Display', 'Georgia', 'serif'],    // Elegancka serif dla nagłówków
        body: ['Inter', 'system-ui', 'sans-serif'],           // Czytelna sans-serif dla tekstu
        logo: ['Playfair Display', 'Georgia', 'serif'],       // Specjalnie dla logo
      },
      fontSize: {
        sm: ['0.875rem', { lineHeight: '1.6rem', letterSpacing: '0.1em' }],
        base: ['1.125rem', { lineHeight: '1.8rem' }], // Minimum 18px dla łatwej czytelności
        lg: ['1.25rem', { lineHeight: '2rem' }],
        xl: ['1.5rem', { lineHeight: '2.1rem', letterSpacing: '-0.01em' }],
        '2xl': ['1.875rem', { lineHeight: '2.3rem', letterSpacing: '-0.01em' }],
        '3xl': ['2.25rem', { lineHeight: '2.6rem', letterSpacing: '-0.01em' }],
        '4xl': ['3rem', { lineHeight: '3.2rem', letterSpacing: '-0.02em' }], // 48px
        '5xl': ['3.5rem', { lineHeight: '3.8rem', letterSpacing: '-0.02em' }], // 56px
        '6xl': ['4rem', { lineHeight: '4.2rem', letterSpacing: '-0.02em' }], // 64px - maksimum dla desktop
      },
      spacing: {
        xs: '0.5rem',
        sm: '1rem',
        md: '1.5rem',
        lg: '2rem',
        xl: '3rem',
        '2xl': '4rem',
        '3xl': '6rem',    // Dodatkowa przestrzeń dla większej elegancji
        '4xl': '8rem',    // Jeszcze większa przestrzeń
      },
      height: {
        hero: '100vh',
        'hero-sm': '85vh',
      },
      maxWidth: {
        content: '64rem',
        'text': '48rem',
        '6xl': '72rem',
      },
      animation: {
        'fade-in': 'fadeIn 1.2s ease-out forwards',
        'slide-up': 'slideUp 1s ease-out forwards',
        parallax: 'parallax 15s linear infinite',
        'float-gentle': 'floatGentle 6s ease-in-out infinite',
        'scale-in': 'scaleIn 0.8s cubic-bezier(0.34, 1.56, 0.64, 1) forwards',
        'hover-lift': 'hoverLift 0.3s ease-out forwards',
      },
      transitionDuration: {
        '400': '400ms',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(15px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        parallax: {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '0% 100%' },
        },
        floatGentle: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-4px)' },
        },
        scaleIn: {
          'from': { opacity: '0', transform: 'scale(0.95)' },
          'to': { opacity: '1', transform: 'scale(1)' },
        },
        hoverLift: {
          'to': { transform: 'translateY(-2px)' },
        },
      },
      transitionTimingFunction: {
        gentle: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'ease-gentle': 'cubic-bezier(0.23, 1, 0.32, 1)',
        'ease-smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'ease-bounce': 'cubic-bezier(0.34, 1.56, 0.64, 1)',
        'ease-out-smooth': 'cubic-bezier(0.16, 1, 0.3, 1)',
      },
      transitionDuration: {
        gentle: '400ms',
        smooth: '300ms',
        soft: '200ms',
      },
      boxShadow: {
        soft: 'var(--shadow-soft)',
        medium: 'var(--shadow-medium)',
        'medium-warm': 'var(--shadow-medium-warm)',
        glow: 'var(--shadow-glow)',
        warm: 'var(--shadow-warm)',
        subtle: 'var(--shadow-subtle)',
        'glass': 'var(--shadow-glass)',
      },
      borderRadius: {
        xl: '0.75rem', 
        lg: '0.5rem',
        md: '0.375rem',
        sm: '0.25rem',
      },
      backgroundImage: {
        'bali-texture': "url('/images/bali-texture.webp')",
        'gradient-hero': 'linear-gradient(135deg, rgba(var(--color-temple), 0.4), rgba(var(--color-ocean), 0.2))',
        'shell-gradient': 'var(--gradient-shell)',
        'warm-gradient': 'var(--gradient-warm)',
        'ocean-gradient': 'var(--gradient-ocean)',
        'sunset-gradient': 'var(--gradient-sunset)',
        'gentle-gradient': 'var(--gradient-gentle)',
      },
      backdropBlur: {
        sm: '4px',
        md: '8px',
        lg: '12px',
        xl: '16px',
      },
    },
  },
  plugins: [],
};