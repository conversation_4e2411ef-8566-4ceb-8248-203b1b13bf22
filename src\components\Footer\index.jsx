'use client';

import React, { useEffect, useRef, useState } from 'react';
import { ArrowUp } from 'lucide-react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { socialLinks } from '@/data/contactData';
import { footerLinks } from '@/data/navigationLinks';

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const footerRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (footerRef.current) {
      observer.observe(footerRef.current);
    }

    return () => {
      if (footerRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <footer
      ref={footerRef}
      id="footer"
      className="bg-off-white border-t border-sage/10 mt-32"
      style={{ maxHeight: '250px' }}
    >
      <div className="max-w-[1140px] mx-auto px-6 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="flex flex-col md:flex-row justify-between items-center"
        >
          <div className="flex items-center space-x-4 mb-6 md:mb-0">
            <span className="font-logo text-dark-gray text-2xl font-normal tracking-[3px]">BAKASANA</span>
            <span className="text-sage/30">•</span>
            <span className="text-sm font-body font-light" style={{ color: '#999999' }}>{currentYear}</span>
          </div>

          <div className="flex justify-center md:justify-end gap-x-6 items-center">
            {socialLinks.map((link) => {
              const Icon = link.icon;
              return (
                <a
                  key={link.label}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="transition-colors duration-300"
                  aria-label={link.aria}
                  style={{ color: '#CCCCCC' }}
                >
                  <Icon className="w-6 h-6 hover:text-sage transition-colors duration-300" strokeWidth={1} />
                </a>
              );
            })}
          </div>
        </motion.div>

        <div className="mt-8 flex justify-center">
          <button
            onClick={scrollToTop}
            className="px-4 py-2 bg-transparent text-sage transition-all duration-300 hover:-translate-y-1 border border-sage/20 hover:border-sage/40"
            aria-label="Powrót do góry"
          >
            <div className="flex items-center gap-2">
              <ArrowUp className="w-4 h-4" strokeWidth={1} />
              <span className="text-sm font-light tracking-wide">Do góry</span>
            </div>
          </button>
        </div>
      </div>
    </footer>
  );
}