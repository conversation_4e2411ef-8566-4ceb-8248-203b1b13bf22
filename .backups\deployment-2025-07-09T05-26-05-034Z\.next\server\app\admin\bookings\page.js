(()=>{var e={};e.id=6907,e.ids=[6907],e.modules={3194:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["bookings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83885)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\bookings\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(s.bind(s,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\bookings\\page.jsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/bookings/page",pathname:"/admin/bookings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4956:(e,t,s)=>{Promise.resolve().then(s.bind(s,87319))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46812:(e,t,s)=>{Promise.resolve().then(s.bind(s,83885))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83885:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\app\\\\admin\\\\bookings\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\admin\\bookings\\page.jsx","default")},87319:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(60687),a=s(43210),l=s(16189);function i(){let[e,t]=(0,a.useState)(!1),[s,i]=(0,a.useState)([]),[n,o]=(0,a.useState)(!0),[d,c]=(0,a.useState)("all"),m=(0,l.useRouter)(),x=async()=>{try{let e=await fetch("/api/admin/bookings");if(e.ok){let t=await e.json();i(t.bookings||[])}}catch(e){console.error("Failed to load bookings:",e)}},p=async(e,t)=>{try{let s=localStorage.getItem("admin-token");(await fetch(`/api/admin/bookings/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({status:t})})).ok&&await x()}catch(e){console.error("Failed to update booking:",e)}},h=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},u=e=>{switch(e){case"pending":return"Oczekuje";case"confirmed":return"Potwierdzona";case"cancelled":return"Anulowana";default:return"Nieznany"}},g=s.filter(e=>"all"===d||e.status===d);return n?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-temple mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-temple",children:"Ładowanie..."})]})}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-rice to-mist",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-temple/10",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("button",{onClick:()=>m.push("/admin"),className:"text-temple hover:text-temple/70 mr-4",children:"← Powr\xf3t"}),(0,r.jsx)("h1",{className:"text-xl font-serif text-temple",children:"Zarządzanie Rezerwacjami"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("span",{className:"text-sm text-wood-light",children:[g.length," rezerwacji"]})})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-soft p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-temple mb-4",children:"Filtry"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{key:"all",label:"Wszystkie",count:s.length},{key:"pending",label:"Oczekujące",count:s.filter(e=>"pending"===e.status).length},{key:"confirmed",label:"Potwierdzone",count:s.filter(e=>"confirmed"===e.status).length},{key:"cancelled",label:"Anulowane",count:s.filter(e=>"cancelled"===e.status).length}].map(e=>(0,r.jsxs)("button",{onClick:()=>c(e.key),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${d===e.key?"bg-temple text-white":"bg-temple/10 text-temple hover:bg-temple/20"}`,children:[e.label," (",e.count,")"]},e.key))})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-soft overflow-hidden",children:0===g.length?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCC5"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-temple mb-2",children:"Brak rezerwacji"}),(0,r.jsx)("p",{className:"text-wood-light",children:"all"===d?"Nie ma jeszcze żadnych rezerwacji.":`Nie ma rezerwacji o statusie "${u(d)}".`})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-temple/10",children:[(0,r.jsx)("thead",{className:"bg-temple/5",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Klient"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Program"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Data"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-temple uppercase tracking-wider",children:"Akcje"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-temple/10",children:g.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-temple/5",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-temple",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:e.email}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:e.phone})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-temple",children:e.program}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:e.destination})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsx)("div",{className:"text-sm text-temple",children:new Date(e.createdAt).toLocaleDateString("pl-PL")}),(0,r.jsx)("div",{className:"text-sm text-wood-light",children:new Date(e.createdAt).toLocaleTimeString("pl-PL")})]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${h(e.status)}`,children:u(e.status)})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:["pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:()=>p(e.id,"confirmed"),className:"text-green-600 hover:text-green-900",children:"Potwierdź"}),(0,r.jsx)("button",{onClick:()=>p(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"})]}),"confirmed"===e.status&&(0,r.jsx)("button",{onClick:()=>p(e.id,"cancelled"),className:"text-red-600 hover:text-red-900",children:"Anuluj"}),"cancelled"===e.status&&(0,r.jsx)("button",{onClick:()=>p(e.id,"pending"),className:"text-blue-600 hover:text-blue-900",children:"Przywr\xf3ć"})]})})]},e.id))})]})})})]})]}):null}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>s(3194));module.exports=r})();