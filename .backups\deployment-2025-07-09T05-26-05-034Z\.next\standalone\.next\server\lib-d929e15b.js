"use strict";exports.id=596,exports.ids=[596],exports.modules={7044:(t,e,i)=>{i.d(e,{B:()=>s});let s=!1},10080:(t,e,i)=>{i.d(e,{s:()=>s});let s=({current:t})=>t?t.ownerDocument.defaultView:null},15124:(t,e,i)=>{i.d(e,{E:()=>r});var s=i(43210);let r=i(7044).B?s.useLayoutEffect:s.useEffect},20172:(t,e,i)=>{i.d(e,{CQ:()=>r,HQ:()=>o,N:()=>h,jA:()=>d,vb:()=>a});var s=i(48219);function r(t){return t.max-t.min}function o(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,o=.5){t.origin=o,t.originPoint=(0,s.k$)(e.min,e.max,t.origin),t.scale=r(i)/r(e),t.translate=(0,s.k$)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,s){n(t.x,e.x,i.x,s?s.originX:void 0),n(t.y,e.y,i.y,s?s.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+r(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+r(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},27642:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return[t("x"),t("y")]}},28337:(t,e,i)=>{function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,r){if("function"==typeof e){let[o,n]=s(r);e=e(void 0!==i?i:t.custom,o,n)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[o,n]=s(r);e=e(void 0!==i?i:t.custom,o,n)}return e}i.d(e,{a:()=>r})},32572:(t,e,i)=>{function s({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function r({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function o(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}i.d(e,{FY:()=>s,bS:()=>o,pA:()=>r})},39853:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}},42485:(t,e,i)=>{i.d(e,{OU:()=>h,Ql:()=>u,Ww:()=>c,hq:()=>o,o4:()=>l});var s=i(48219),r=i(67606);function o(t,e,i){return i+e*(t-i)}function n(t,e,i,s,r){return void 0!==r&&(t=s+r*(t-s)),s+i*(t-s)+e}function a(t,e=0,i=1,s,r){t.min=n(t.min,e,i,s,r),t.max=n(t.max,e,i,s,r)}function l(t,{x:e,y:i}){a(t.x,e.translate,e.scale,e.originPoint),a(t.y,i.translate,i.scale,i.originPoint)}function h(t,e,i,s=!1){let o,n,a=i.length;if(a){e.x=e.y=1;for(let h=0;h<a;h++){n=(o=i[h]).projectionDelta;let{visualElement:a}=o.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&c(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),n&&(e.x*=n.x.scale,e.y*=n.y.scale,l(t,n)),s&&(0,r.HD)(o.latestValues)&&c(t,o.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function u(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,r,o=.5){let n=(0,s.k$)(t.min,t.max,o);a(t,e,i,n,r)}function c(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},44693:(t,e,i)=>{i.d(e,{K:()=>r});var s=i(28337);function r(t,e,i){let r=t.getProps();return(0,s.a)(r,e,void 0!==i?i:r.custom,t)}},54538:(t,e,i)=>{i.d(e,{ge:()=>n,xU:()=>r});let s=()=>({translate:0,scale:1,origin:0,originPoint:0}),r=()=>({x:s(),y:s()}),o=()=>({min:0,max:0}),n=()=>({x:o(),y:o()})},54642:(t,e,i)=>{i.d(e,{U:()=>n});var s=i(48219),r=i(27292),o=i(44693);function n(t,e){let{transitionEnd:i={},transition:n={},...a}=(0,o.K)(t,e)||{};for(let e in a={...a,...i}){var l;let i=(l=a[e],(0,r.p)(l)?l[l.length-1]||0:l);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,s.OQ)(i))}}},57529:(t,e,i)=>{i.d(e,{O:()=>a,e:()=>n});var s=i(56570),r=i(90567),o=i(61328);function n(t){return(0,s.N)(t.animate)||o._.some(e=>(0,r.w)(t[e]))}function a(t){return!!(n(t)||t.variants)}},61328:(t,e,i)=>{i.d(e,{U:()=>s,_:()=>r});let s=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],r=["initial",...s]},64496:(t,e,i)=>{i.d(e,{X:()=>s});class s{constructor(t){this.isMounted=!1,this.node=t}update(){}}},67283:(t,e,i)=>{i.d(e,{g:()=>o});var s=i(2683),r=i(48219);function o(t,e){let i=t.getValue("willChange");if((0,r.SS)(i)&&i.add)return i.add(e);if(!i&&s.W9.WillChange){let i=new s.W9.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},67606:(t,e,i)=>{function s(t){return void 0===t||1===t}function r({scale:t,scaleX:e,scaleY:i}){return!s(t)||!s(e)||!s(i)}function o(t){return r(t)||n(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function n(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>o,vF:()=>n,vk:()=>r})},67886:(t,e,i)=>{i.d(e,{I:()=>s});let s=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},72789:(t,e,i)=>{i.d(e,{M:()=>r});var s=i(43210);function r(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},73426:(t,e,i)=>{i.d(e,{w:()=>r});let s=(t,e)=>Math.abs(t-e);function r(t,e){return Math.sqrt(s(t.x,e.x)**2+s(t.y,e.y)**2)}},82692:(t,e,i)=>{i.d(e,{P:()=>eH});var s,r,o=i(2683),n=i(56570),a=i(23007),l=i(27292);function h(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}var u=i(90567),d=i(61328);let c=d._.length;var p=i(44693);let m=[...d.U].reverse(),f=d.U.length;function g(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y(){return{animate:g(!0),whileInView:g(),whileHover:g(),whileTap:g(),whileDrag:g(),whileFocus:g(),exit:g()}}var v=i(64496);class x extends v.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,a._)(t,e,i))),i=y(),s=!0,r=e=>(i,s)=>{let r=(0,p.K)(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function o(o){let{props:a}=t,g=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<c;t++){let s=d._[t],r=e.props[s];((0,u.w)(r)||!1===r)&&(i[s]=r)}return i}(t.parent)||{},y=[],v=new Set,x={},S=1/0;for(let e=0;e<f;e++){var T,P;let d=m[e],c=i[d],p=void 0!==a[d]?a[d]:g[d],f=(0,u.w)(p),V=d===o?c.isActive:null;!1===V&&(S=e);let w=p===g[d]&&p!==a[d]&&f;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),c.protectedKeys={...x},!c.isActive&&null===V||!p&&!c.prevProp||(0,n.N)(p)||"boolean"==typeof p)continue;let A=(T=c.prevProp,"string"==typeof(P=p)?P!==T:!!Array.isArray(P)&&!h(P,T)),D=A||d===o&&c.isActive&&!w&&f||e>S&&f,b=!1,j=Array.isArray(p)?p:[p],C=j.reduce(r(d),{});!1===V&&(C={});let{prevResolvedValues:R={}}=c,B={...R,...C},k=e=>{D=!0,v.has(e)&&(b=!0,v.delete(e)),c.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in B){let e=C[t],i=R[t];if(x.hasOwnProperty(t))continue;let s=!1;((0,l.p)(e)&&(0,l.p)(i)?h(e,i):e===i)?void 0!==e&&v.has(t)?k(t):c.protectedKeys[t]=!0:null!=e?k(t):v.add(t)}c.prevProp=p,c.prevResolvedValues=C,c.isActive&&(x={...x,...C}),s&&t.blockInitialAnimation&&(D=!1);let L=!(w&&A)||b;D&&L&&y.push(...j.map(t=>({animation:t,options:{type:d}})))}if(v.size){let e={};if("boolean"!=typeof a.initial){let i=(0,p.K)(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}v.forEach(i=>{let s=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=s??null}),y.push({animation:e})}let V=!!y.length;return s&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(V=!1),s=!1,V?e(y):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=y(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,n.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let S=0;class T extends v.X{constructor(){super(...arguments),this.id=S++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var P=i(65820),V=i(4980),w=i(60687),A=i(48219),D=i(43210),b=i(86044),j=i(12157),C=i(83641);let R={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function B(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let k={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!A.px.test(t))return t;else t=parseFloat(t);let i=B(t,e.target.x),s=B(t,e.target.y);return`${i}% ${s}%`}},L={};class M extends D.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:r}=t;for(let t in F)L[t]=F[t],(0,A.j4)(t)&&(L[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&s&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),R.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:r}=this.props,{projection:o}=i;return o&&(o.isPresent=r,s||t.layoutDependency!==e||void 0===e||t.isPresent!==r?o.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?o.promote():o.relegate()||A.Gt.postRender(()=>{let t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),A.k2.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function E(t){let[e,i]=(0,b.xQ)(),s=(0,D.useContext)(j.L);return(0,w.jsx)(M,{...t,layoutGroup:s,switchLayoutGroup:(0,D.useContext)(C.N),isPresent:e,safeToRemove:i})}let F={borderRadius:{...k,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:k,borderTopRightRadius:k,borderBottomLeftRadius:k,borderBottomRightRadius:k,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=A.f.parse(t);if(s.length>5)return t;let r=A.f.createTransformer(t),o=+("number"!=typeof s[0]),n=i.x.scale*e.x,a=i.y.scale*e.y;s[0+o]/=n,s[1+o]/=a;let l=(0,A.k$)(n,a,.5);return"number"==typeof s[2+o]&&(s[2+o]/=l),"number"==typeof s[3+o]&&(s[3+o]/=l),r(s)}}};var U=i(75944),O=i(80722);let I=(t,e)=>t.depth-e.depth;class ${constructor(){this.children=[],this.isDirty=!1}add(t){(0,o.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,o.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(I),this.isDirty=!1,this.children.forEach(t)}}function N(t){return(0,A.SS)(t)?t.get():t}let W=["TopLeft","TopRight","BottomLeft","BottomRight"],Q=W.length,H=t=>"string"==typeof t?parseFloat(t):t,X=t=>"number"==typeof t||A.px.test(t);function z(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let G=q(0,.5,o.yT),Y=q(.5,.95,o.lQ);function q(t,e,i){return s=>s<t?0:s>e?1:i((0,o.qB)(t,e,s))}function K(t,e){t.min=e.min,t.max=e.max}function _(t,e){K(t.x,e.x),K(t.y,e.y)}function Z(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var J=i(42485),tt=i(20172);function te(t,e,i,s,r){return t-=e,t=(0,J.hq)(t,1/i,s),void 0!==r&&(t=(0,J.hq)(t,1/r,s)),t}function ti(t,e,[i,s,r],o,n){!function(t,e=0,i=1,s=.5,r,o=t,n=t){if(A.rq.test(e)&&(e=parseFloat(e),e=(0,A.k$)(n.min,n.max,e/100)-n.min),"number"!=typeof e)return;let a=(0,A.k$)(o.min,o.max,s);t===o&&(a-=e),t.min=te(t.min,e,i,a,r),t.max=te(t.max,e,i,a,r)}(t,e[i],e[s],e[r],e.scale,o,n)}let ts=["x","scaleX","originX"],tr=["y","scaleY","originY"];function to(t,e,i,s){ti(t.x,e,ts,i?i.x:void 0,s?s.x:void 0),ti(t.y,e,tr,i?i.y:void 0,s?s.y:void 0)}var tn=i(54538);function ta(t){return 0===t.translate&&1===t.scale}function tl(t){return ta(t.x)&&ta(t.y)}function th(t,e){return t.min===e.min&&t.max===e.max}function tu(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function td(t,e){return tu(t.x,e.x)&&tu(t.y,e.y)}function tc(t){return(0,tt.CQ)(t.x)/(0,tt.CQ)(t.y)}function tp(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class tm{constructor(){this.members=[]}add(t){(0,o.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,o.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var tf=i(27642),tg=i(67606);let ty={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},tv=["","X","Y","Z"],tx={visibility:"hidden"},tS=0;function tT(t,e,i,s){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),s&&(s[t]=0))}function tP({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=tS++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,A.Qu.value&&(ty.nodes=ty.calculatedTargetDeltas=ty.calculatedProjections=0),this.nodes.forEach(tA),this.nodes.forEach(tk),this.nodes.forEach(tL),this.nodes.forEach(tD),A.Qu.addProjectionMetrics&&A.Qu.addProjectionMetrics(ty)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new $)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new o.vY),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,A.xZ)(e)&&!(0,A.h1)(e),this.instance=e;let{layoutId:i,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.kB.now(),s=({timestamp:r})=>{let o=r-i;o>=250&&((0,A.WG)(s),t(o-e))};return A.Gt.setup(s,!0),()=>(0,A.WG)(s)}(s,250),R.hasAnimatedSinceResize&&(R.hasAnimatedSinceResize=!1,this.nodes.forEach(tB))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||r.getDefaultTransition()||tI,{onLayoutAnimationStart:n,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!td(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...(0,A.rU)(o,"layout"),onPlay:n,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||tB(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,A.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(tM),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if((void 0).MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,O.P)(i);if((void 0).MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;(void 0).MotionCancelOptimisedAnimation(s,"transform",A.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(tj);return}this.isUpdating||this.nodes.forEach(tC),this.isUpdating=!1,this.nodes.forEach(tR),this.nodes.forEach(tV),this.nodes.forEach(tw),this.clearAllSnapshots();let t=A.kB.now();A.uv.delta=(0,o.qE)(0,1e3/60,t-A.uv.timestamp),A.uv.timestamp=t,A.uv.isProcessing=!0,A.PP.update.process(A.uv),A.PP.preRender.process(A.uv),A.PP.render.process(A.uv),A.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,A.k2.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(tb),this.sharedNodes.forEach(tE)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,A.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){A.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,tt.CQ)(this.snapshot.measuredBox.x)||(0,tt.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,tn.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!tl(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||(0,tg.HD)(this.latestValues)||o)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),tW((e=s).x),tW(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return(0,tn.ge)();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(tH))){let{scroll:t}=this.root;t&&((0,J.Ql)(e.x,t.offset.x),(0,J.Ql)(e.y,t.offset.y))}return e}removeElementScroll(t){let e=(0,tn.ge)();if(_(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:r,options:o}=s;s!==this.root&&r&&o.layoutScroll&&(r.wasRoot&&_(e,t),(0,J.Ql)(e.x,r.offset.x),(0,J.Ql)(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=(0,tn.ge)();_(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,J.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,tg.HD)(s.latestValues)&&(0,J.Ww)(i,s.latestValues)}return(0,tg.HD)(this.latestValues)&&(0,J.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,tn.ge)();_(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,tg.HD)(i.latestValues))continue;(0,tg.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,tn.ge)();_(s,i.measurePageBox()),to(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,tg.HD)(this.latestValues)&&to(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==A.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:r}=this.options;if(this.layout&&(s||r)){if(this.resolvedRelativeTargetAt=A.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,tn.ge)(),this.relativeTargetOrigin=(0,tn.ge)(),(0,tt.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,tn.ge)(),this.targetWithTransforms=(0,tn.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,tt.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_(this.target,this.layout.layoutBox),(0,J.o4)(this.target,this.targetDelta)):_(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,tn.ge)(),this.relativeTargetOrigin=(0,tn.ge)(),(0,tt.jA)(this.relativeTargetOrigin,this.target,t.target),_(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}A.Qu.value&&ty.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,tg.vk)(this.parent.latestValues)||(0,tg.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===A.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;_(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,n=this.treeScale.y;(0,J.OU)(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=(0,tn.ge)());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(Z(this.prevProjectionDelta.x,this.projectionDelta.x),Z(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,tt.vb)(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===n&&tp(this.projectionDelta.x,this.prevProjectionDelta.x)&&tp(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),A.Qu.value&&ty.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,tn.xU)(),this.projectionDelta=(0,tn.xU)(),this.projectionDeltaWithTransform=(0,tn.xU)()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,r=s?s.latestValues:{},o={...this.latestValues},n=(0,tn.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=(0,tn.ge)(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(tO));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(tF(n.x,t.x,s),tF(n.y,t.y,s),this.setTargetDelta(n),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,g;(0,tt.jA)(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,g=s,tU(p.x,m.x,f.x,g),tU(p.y,m.y,f.y,g),i&&(h=this.relativeTarget,c=i,th(h.x,c.x)&&th(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=(0,tn.ge)()),_(i,this.relativeTarget)}l&&(this.animationValues=o,function(t,e,i,s,r,o){r?(t.opacity=(0,A.k$)(0,i.opacity??1,G(s)),t.opacityExit=(0,A.k$)(e.opacity??1,0,Y(s))):o&&(t.opacity=(0,A.k$)(e.opacity??1,i.opacity??1,s));for(let r=0;r<Q;r++){let o=`border${W[r]}Radius`,n=z(e,o),a=z(i,o);(void 0!==n||void 0!==a)&&(n||(n=0),a||(a=0),0===n||0===a||X(n)===X(a)?(t[o]=Math.max((0,A.k$)(H(n),H(a),s),0),(A.rq.test(a)||A.rq.test(n))&&(t[o]+="%")):t[o]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,A.k$)(e.rotate||0,i.rotate||0,s))}(o,r,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&((0,A.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=A.Gt.update(()=>{R.hasAnimatedSinceResize=!0,A.qU.layout++,this.motionValue||(this.motionValue=(0,A.OQ)(0)),this.currentAnimation=(0,U.z)(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{A.qU.layout--},onComplete:()=>{A.qU.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:r}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&tQ(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,tn.ge)();let e=(0,tt.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,tt.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}_(e,i),(0,J.Ww)(e,r),(0,tt.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new tm),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&tT("z",t,s,this.animationValues);for(let e=0;e<tv.length;e++)tT(`rotate${tv[e]}`,t,s,this.animationValues),tT(`skew${tv[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return tx;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=N(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=N(t?.pointerEvents)||""),this.hasProjected&&!(0,tg.HD)(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",r=t.x.translate/e.x,o=t.y.translate/e.y,n=i?.z||0;if((r||o||n)&&(s=`translate3d(${r}px, ${o}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:o,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),r&&(s+=`rotateX(${r}deg) `),o&&(s+=`rotateY(${o}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:o,y:n}=this.projectionDelta;for(let t in e.transformOrigin=`${100*o.origin}% ${100*n.origin}% 0`,s.animationValues?e.opacity=s===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=s===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,L){if(void 0===r[t])continue;let{correct:i,applyTo:o,isCSSVariable:n}=L[t],a="none"===e.transform?r[t]:i(r[t],s);if(o){let t=o.length;for(let i=0;i<t;i++)e[o[i]]=a}else n?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?N(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(tj),this.root.sharedNodes.clear()}}}function tV(t){t.updateLayout()}function tw(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:r}=t.options,o=e.source!==t.layout.source;"size"===r?(0,tf.X)(t=>{let s=o?e.measuredBox[t]:e.layoutBox[t],r=(0,tt.CQ)(s);s.min=i[t].min,s.max=s.min+r}):tQ(r,e.layoutBox,i)&&(0,tf.X)(s=>{let r=o?e.measuredBox[s]:e.layoutBox[s],n=(0,tt.CQ)(i[s]);r.max=r.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,tn.xU)();(0,tt.vb)(n,i,e.layoutBox);let a=(0,tn.xU)();o?(0,tt.vb)(a,t.applyTransform(s,!0),e.measuredBox):(0,tt.vb)(a,i,e.layoutBox);let l=!tl(n),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:r,layout:o}=s;if(r&&o){let n=(0,tn.ge)();(0,tt.jA)(n,e.layoutBox,r.layoutBox);let a=(0,tn.ge)();(0,tt.jA)(a,i,o.layoutBox),td(n,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function tA(t){A.Qu.value&&ty.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function tD(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function tb(t){t.clearSnapshot()}function tj(t){t.clearMeasurements()}function tC(t){t.isLayoutDirty=!1}function tR(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function tB(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function tk(t){t.resolveTargetDelta()}function tL(t){t.calcProjection()}function tM(t){t.resetSkewAndRotation()}function tE(t){t.removeLeadSnapshot()}function tF(t,e,i){t.translate=(0,A.k$)(e.translate,0,i),t.scale=(0,A.k$)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function tU(t,e,i,s){t.min=(0,A.k$)(e.min,i.min,s),t.max=(0,A.k$)(e.max,i.max,s)}function tO(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let tI={duration:.45,ease:[.4,0,.1,1]},t$=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),tN=t$("applewebkit/")&&!t$("chrome/")?Math.round:o.lQ;function tW(t){t.min=tN(t.min),t.max=tN(t.max)}function tQ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,tt.HQ)(tc(e),tc(i),.2)}function tH(t){return t!==t.root&&t.scroll?.wasRoot}var tX=i(58902);let tz=tP({attachResizeListener:(t,e)=>(0,tX.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tG={current:void 0},tY=tP({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tG.current){let t=new tz({});t.mount(void 0),t.setOptions({layoutScroll:!0}),tG.current=t}return tG.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===(void 0).getComputedStyle(t).position}),tq={pan:{Feature:V.f},drag:{Feature:P.w,ProjectionNode:tY,MeasureLayout:E}};var tK=i(70478),t_=i(13500),tZ=i(30765);let tJ=new WeakMap,t0=new WeakMap,t1=t=>{let e=tJ.get(t.target);e&&e(t)},t2=t=>{t.forEach(t1)},t9={some:0,all:1};class t4 extends v.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:r}=t,o={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:t9[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;t0.has(i)||t0.set(i,{});let s=t0.get(i),r=JSON.stringify(e);return s[r]||(s[r]=new IntersectionObserver(t2,{root:t,...e})),s[r]}(e);return tJ.set(t,i),s.observe(t),()=>{tJ.delete(t),s.unobserve(t)}}(this.node.current,o,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),o=e?i:s;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let t5={inView:{Feature:t4},tap:{Feature:tZ.H},focus:{Feature:t_.c},hover:{Feature:tK.e}};var t6=i(43436),t3=i(32582),t7=i(34213),t8=i(71754),et=i(7044);let ee={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ei={};for(let t in ee)ei[t]={isEnabled:e=>ee[t].some(t=>!!e[t])};let es=Symbol.for("motionComponentSymbol");var er=i(39853),eo=i(51756),en=i(21279),ea=i(15124);function el(t,{layout:e,layoutId:i}){return A.fu.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!L[t]||"opacity"===t)}let eh={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eu=A.Us.length;function ed(t,e,i){let{style:s,vars:r,transformOrigin:o}=t,n=!1,a=!1;for(let t in e){let i=e[t];if(A.fu.has(t)){n=!0;continue}if((0,A.j4)(t)){r[t]=i;continue}{let e=(0,A.eK)(i,A.Wh[t]);t.startsWith("origin")?(a=!0,o[t]=e):s[t]=e}}if(!e.transform&&(n||i?s.transform=function(t,e,i){let s="",r=!0;for(let o=0;o<eu;o++){let n=A.Us[o],a=t[n];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!n.startsWith("scale"):0===parseFloat(a))||i){let t=(0,A.eK)(a,A.Wh[n]);if(!l){r=!1;let e=eh[n]||n;s+=`${e}(${t}) `}i&&(e[n]=t)}}return s=s.trim(),i?s=i(e,r?"":s):r&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=o;s.transformOrigin=`${t} ${e} ${i}`}}let ec=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ep(t,e,i){for(let s in e)(0,A.SS)(e[s])||el(s,i)||(t[s]=e[s])}let em={offset:"stroke-dashoffset",array:"stroke-dasharray"},ef={offset:"strokeDashoffset",array:"strokeDasharray"};function eg(t,{attrX:e,attrY:i,attrScale:s,pathLength:r,pathSpacing:o=1,pathOffset:n=0,...a},l,h,u){if(ed(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==r&&function(t,e,i=1,s=0,r=!0){t.pathLength=1;let o=r?em:ef;t[o.offset]=A.px.transform(-s);let n=A.px.transform(e),a=A.px.transform(i);t[o.array]=`${n} ${a}`}(d,r,o,n,!1)}let ey=()=>({...ec(),attrs:{}}),ev=t=>"string"==typeof t&&"svg"===t.toLowerCase(),ex=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eS(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ex.has(t)}let eT=t=>!eS(t);try{!function(t){t&&(eT=e=>e.startsWith("on")?!eS(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let eP=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eV(t){if("string"!=typeof t||t.includes("-"));else if(eP.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var ew=i(57529),eA=i(28337),eD=i(72789);let eb=t=>(e,i)=>{let s=(0,D.useContext)(t7.A),r=(0,D.useContext)(en.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},o=s(t,{});for(let t in o)r[t]=N(o[t]);let{initial:a,animate:l}=t,h=(0,ew.e)(t),u=(0,ew.O)(t);e&&u&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let d=!!i&&!1===i.initial,c=(d=d||!1===a)?l:a;if(c&&"boolean"!=typeof c&&!(0,n.N)(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let s=(0,eA.a)(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?o():(0,eD.M)(o)};function ej(t,e,i){let{style:s}=t,r={};for(let o in s)((0,A.SS)(s[o])||e.style&&(0,A.SS)(e.style[o])||el(o,t)||i?.getValue(o)?.liveStyle!==void 0)&&(r[o]=s[o]);return r}let eC={useVisualState:eb({scrapeMotionValuesFromProps:ej,createRenderState:ec})};function eR(t,e,i){let s=ej(t,e,i);for(let i in t)((0,A.SS)(t[i])||(0,A.SS)(e[i]))&&(s[-1!==A.Us.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let eB={useVisualState:eb({scrapeMotionValuesFromProps:eR,createRenderState:ey})};var ek=i(92953);let eL={current:null},eM={current:!1},eE=new WeakMap,eF=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class eU{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:r,visualState:o},n={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=A.hP,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.kB.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,A.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=n,this.blockInitialAnimation=!!r,this.isControllingVariants=(0,ew.e)(e),this.isVariantNode=(0,ew.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==a[t]&&(0,A.SS)(e)&&e.set(a[t],!1)}}mount(t){this.current=t,eE.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eM.current||function(){if(eM.current=!0,et.B)if((void 0).matchMedia){let t=(void 0).matchMedia("(prefers-reduced-motion)"),e=()=>eL.current=t.matches;t.addListener(e),e()}else eL.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||eL.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,A.WG)(this.notifyUpdate),(0,A.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=A.fu.has(t);s&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&A.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=e.on("renderRequest",this.scheduleRender);(void 0).MotionCheckAppearSync&&(i=(void 0).MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),o(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in ei){let e=ei[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,tn.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<eF.length;e++){let i=eF[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let r=e[s],o=i[s];if((0,A.SS)(r))t.addValue(s,r);else if((0,A.SS)(o))t.addValue(s,(0,A.OQ)(r,{owner:t}));else if(o!==r)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(s);t.addValue(s,(0,A.OQ)(void 0!==e?e:r,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,A.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&((0,o.iW)(i)||(0,o.$X)(i))?i=parseFloat(i):!(0,A.tD)(i)&&A.f.test(e)&&(i=(0,A.Ju)(t,e)),this.setBaseTarget(t,(0,A.SS)(i)?i.get():i)),(0,A.SS)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=(0,eA.a)(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||(0,A.SS)(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new o.vY),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class eO extends eU{constructor(){super(...arguments),this.KeyframeResolver=A.KN}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,A.SS)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function eI(t,{style:e,vars:i},s,r){for(let o in Object.assign(t.style,e,r&&r.getProjectionStyles(s)),i)t.style.setProperty(o,i[o])}class e$ extends eO{constructor(){super(...arguments),this.type="html",this.renderInstance=eI}readValueFromInstance(t,e){if(A.fu.has(e))return this.projection?.isProjecting?(0,A.zs)(e):(0,A.Ib)(t,e);{let i=(void 0).getComputedStyle(t),s=((0,A.j4)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,ek.m)(t,e)}build(t,e,i){ed(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return ej(t,e,i)}}var eN=i(67886);let eW=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class eQ extends eO{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tn.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(A.fu.has(e)){let t=(0,A.Df)(e);return t&&t.default||0}return e=eW.has(e)?e:(0,eN.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eR(t,e,i)}build(t,e,i){eg(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in eI(t,e,void 0,s),e.attrs)t.setAttribute(eW.has(i)?i:(0,eN.I)(i),e.attrs[i])}mount(t){this.isSVGTag=ev(t.tagName),super.mount(t)}}let eH=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((s={animation:{Feature:x},exit:{Feature:T},...t5,...tq,layout:{ProjectionNode:tY,MeasureLayout:E}},r=(t,e)=>eV(t)?new eQ(e):new e$(e,{allowProjection:t!==D.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:r}){function o(t,o){var n,a,l;let h,u={...(0,D.useContext)(t3.Q),...t,layoutId:function({layoutId:t}){let e=(0,D.useContext)(j.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:d}=u,c=(0,t8.z)(t),p=s(t,d);if(!d&&et.B){a=0,l=0,(0,D.useContext)(t6.Y).strict;let t=function(t){let{drag:e,layout:i}=ei;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,r){let{visualElement:o}=(0,D.useContext)(t7.A),n=(0,D.useContext)(t6.Y),a=(0,D.useContext)(en.t),l=(0,D.useContext)(t3.Q).reducedMotion,h=(0,D.useRef)(null);s=s||n.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:o,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,D.useContext)(C.N);u&&!u.projection&&r&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:r,layout:o,drag:n,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!n||a&&(0,er.X)(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,r,d);let c=(0,D.useRef)(!1);(0,D.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[eo.n],m=(0,D.useRef)(!!p&&!(void 0).MotionHandoffIsComplete?.(p)&&(void 0).MotionHasOptimisedAnimation?.(p));return(0,ea.E)(()=>{u&&(c.current=!0,undefined.MotionIsMounted=!0,u.updateFeatures(),A.k2.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),u}(r,p,u,e,t.ProjectionNode)}return(0,w.jsxs)(t7.A.Provider,{value:c,children:[h&&c.visualElement?(0,w.jsx)(h,{visualElement:c.visualElement,...u}):null,i(r,t,(n=c.visualElement,(0,D.useCallback)(t=>{t&&p.onMount&&p.onMount(t),n&&(t?n.mount(t):n.unmount()),o&&("function"==typeof o?o(t):(0,er.X)(o)&&(o.current=t))},[n])),p,d,c.visualElement)]})}t&&function(t){for(let e in t)ei[e]={...ei[e],...t[e]}}(t),o.displayName=`motion.${"string"==typeof r?r:`create(${r.displayName??r.name??""})`}`;let n=(0,D.forwardRef)(o);return n[es]=r,n}({...eV(t)?eB:eC,preloadedFeatures:s,useRender:function(t=!1){return(e,i,s,{latestValues:r},o)=>{let n=(eV(e)?function(t,e,i,s){let r=(0,D.useMemo)(()=>{let i=ey();return eg(i,e,ev(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ep(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return ep(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,D.useMemo)(()=>{let i=ec();return ed(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,r,o,e),a=function(t,e,i){let s={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(eT(r)||!0===i&&eS(r)||!e&&!eS(r)||t.draggable&&r.startsWith("onDrag"))&&(s[r]=t[r]);return s}(i,"string"==typeof e,t),l=e!==D.Fragment?{...a,...n,ref:s}:{},{children:h}=i,u=(0,D.useMemo)(()=>(0,A.SS)(h)?h.get():h,[h]);return(0,D.createElement)(e,{...l,children:u})}}(e),createVisualElement:r,Component:t})}))},90567:(t,e,i)=>{i.d(e,{w:()=>s});function s(t){return"string"==typeof t||Array.isArray(t)}},92953:(t,e,i)=>{i.d(e,{L:()=>n,m:()=>o});var s=i(32572),r=i(42485);function o(t,e){return(0,s.FY)((0,s.bS)(t.getBoundingClientRect(),e))}function n(t,e,i){let s=o(t,i),{scroll:n}=e;return n&&((0,r.Ql)(s.x,n.offset.x),(0,r.Ql)(s.y,n.offset.y)),s}}};