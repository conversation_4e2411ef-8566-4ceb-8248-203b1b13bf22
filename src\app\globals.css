@tailwind base;
@tailwind components;
@tailwind utilities;

/* ==========================================================================
   CSS Variables - Paleta zgodna z wymaganiami
   ========================================================================== */
@layer base {
  :root {
    /* NOWA ELEGANCKA PALETA - SPA HOTEL 5* */
    --color-primary: 255 255 255;      /* Biały #FFFFFF */
    --color-off-white: 250 250 248;    /* Off-white #FAFAF8 */
    --color-sage: 124 152 133;         /* Szałwiowy #7C9885 */
    --color-sand: 212 181 160;         /* <PERSON>iep<PERSON>y piasek #D4B5A0 */
    --color-text-gray: 102 102 102;    /* Szary tekst #666666 */
    --color-dark-gray: 44 62 80;       /* Ciemnoszary #2C3E50 */

    /* Delikatne odcienie */
    --color-sage-light: 168 196 178;   /* Jaśniejszy szałwiowy */
    --color-sage-dark: 107 138 120;    /* Ciemniejszy szałwiowy */
    --color-sand-light: 229 205 184;   /* Jaśniejszy piasek */

    /* NOWE FONTY - ELEGANCKIE */
    --font-playfair: 'Playfair Display', 'Georgia', 'Times New Roman', serif;
    --font-source-sans: 'Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', sans-serif;
    
    /* CIENIE - ULTRA DELIKATNE SPA STYLE */
    --shadow-soft: 0 2px 20px rgba(0, 0, 0, 0.03);
    --shadow-medium: 0 5px 25px rgba(0, 0, 0, 0.05);
    --shadow-subtle: 0 1px 10px rgba(0, 0, 0, 0.02);
    --shadow-sage: 0 5px 25px rgba(124, 152, 133, 0.15);
    --shadow-elegant: 0 8px 35px rgba(124, 152, 133, 0.2);

    /* Timing functions */
    --ease-gentle: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --ease-out-smooth: cubic-bezier(0.16, 1, 0.3, 1);
  }

  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Delikatny zoom na minus - pokazuje więcej treści */
    zoom: 0.95;
  }

  body {
    @apply font-body antialiased;
    background: rgb(var(--color-primary));
    color: rgb(var(--color-text-gray));
    letter-spacing: 0.01em;
    line-height: 1.6;
    font-family: var(--font-source-sans);
    padding-top: 80px; /* Kompensacja dla fixed navbar */
  }

  /* Dodatkowe style dla retreat hero */
  .retreat-hero-bg {
    background-image: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4));
  }

  /* Lepsze responsywne nagłówki */
  @media (max-width: 640px) {
    h1 {
      font-size: 2.5rem;
      line-height: 1.1;
    }
    
    h2 {
      font-size: 2rem;
      line-height: 1.2;
    }
  }

  /* NOWE ELEGANCKIE PRZYCISKI - SPA STYLE */
  .btn-primary {
    @apply inline-flex items-center justify-center px-12 py-4 text-sm font-normal tracking-wide;
    background: rgb(var(--color-sage));
    color: rgb(var(--color-primary));
    border: none;
    transition: all 0.4s ease;
    text-decoration: none;
    letter-spacing: 1px;
    box-shadow: 0 5px 25px rgba(124, 152, 133, 0.2);
  }

  .btn-primary:hover {
    background: rgb(var(--color-sage-dark));
    transform: translateY(-3px);
    box-shadow: 0 8px 35px rgba(124, 152, 133, 0.3);
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-12 py-4 text-sm font-light tracking-wide;
    background: transparent;
    border: 1px solid rgb(var(--color-sage));
    color: rgb(var(--color-sage));
    transition: all 0.4s ease;
    text-decoration: none;
    letter-spacing: 1px;
  }

  .btn-secondary:hover {
    background: rgb(var(--color-sage));
    color: rgb(var(--color-primary));
  }

  /* HERO PRZYCISKI - DELIKATNE I ELEGANCKIE */
  .hero-btn-primary {
    @apply inline-flex items-center justify-center px-10 py-3 text-sm font-light uppercase;
    background: rgba(255, 255, 255, 0.95);
    color: rgb(var(--color-dark-gray));
    border: none;
    transition: all 0.4s ease;
    text-decoration: none;
    letter-spacing: 3px;
    font-size: 14px;
    font-weight: 300;
  }

  .hero-btn-primary:hover {
    background: rgb(var(--color-primary));
  }

  .hero-btn-secondary {
    @apply inline-flex items-center justify-center px-10 py-3 text-sm font-light uppercase;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.8);
    color: rgb(var(--color-primary));
    transition: all 0.4s ease;
    text-decoration: none;
    letter-spacing: 3px;
    font-size: 14px;
    font-weight: 300;
    backdrop-filter: blur(10px);
  }

  .hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.8);
  }

}

@layer utilities {
  /* NOWE WYMIARY - SPA HOTEL 5* */
  .section-padding {
    @apply py-32;
  }

  .container-spa {
    @apply max-w-[1140px] mx-auto px-6;
  }

  .max-width-content {
    @apply max-w-[1140px] mx-auto;
  }

  .max-width-text {
    @apply max-w-[700px] mx-auto;
  }

  /* NOWE NAGŁÓWKI */
  .heading-section {
    @apply text-[42px] font-display font-normal text-dark-gray mb-16 leading-tight;
  }

  .text-body {
    @apply text-[17px] leading-[1.8] text-text-gray;
  }

  /* ELEGANCKIE LINIE ODDZIELAJĄCE */
  .divider-elegant {
    border-bottom: 1px solid #E5E5E5;
  }

  .divider-gradient {
    background: linear-gradient(90deg, transparent, #E5E5E5, transparent);
    height: 1px;
  }

  /* BIAŁE PRZESTRZENIE - MINIMUM 60PX */
  .spacing-elegant {
    @apply mb-16; /* 64px */
  }

  .spacing-section {
    @apply py-32; /* 128px góra/dół */
  }

  .spacing-element {
    @apply mb-16; /* 64px między elementami */
  }

  .text-balance {
    text-wrap: balance;
  }

  .text-pretty {
    text-wrap: pretty;
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }

  .shadow-subtle {
    box-shadow: var(--shadow-subtle);
  }

  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }

  /* Profesjonalna galeria - jednolita siatka */
  .gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2px;
  }

  .gallery img {
    aspect-ratio: 1/1;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.22, 1, 0.36, 1);
    cursor: pointer;
  }

  .gallery img:hover {
    transform: scale(1.02);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes floatGentle {
    0%, 100% { 
      transform: translateY(0px); 
    }
    50% { 
      transform: translateY(-4px); 
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes hoverLift {
    to {
      transform: translateY(-2px);
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .animate-fade-in {
    animation: fadeIn 1.2s ease forwards;
  }

  .animate-slide-up {
    animation: slideUp 1s ease forwards;
  }

  .animate-float {
    animation: floatGentle 6s ease-in-out infinite;
  }

  .animate-scale-in {
    animation: scaleIn 0.8s ease forwards;
  }

  .animate-hover-lift {
    animation: hoverLift 0.3s ease forwards;
  }

  .delay-100 { animation-delay: 100ms; }
  .delay-200 { animation-delay: 200ms; }
  .delay-300 { animation-delay: 300ms; }
  .delay-400 { animation-delay: 400ms; }
  .delay-500 { animation-delay: 500ms; }

  .aspect-square {
    aspect-ratio: 1/1;
  }

  .aspect-video {
    aspect-ratio: 16/9;
  }

  .aspect-photo {
    aspect-ratio: 4/3;
  }

  .aspect-portrait {
    aspect-ratio: 3/4;
  }

  .image-container {
    @apply relative overflow-hidden;
  }

  .image-container img {
    @apply w-full h-full object-cover transition-transform duration-700;
  }

  .image-container:hover img {
    @apply transform translate-y-[-2px];
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    --tw-ring-color: rgb(var(--color-accent) / 0.4);
  }

  .interactive {
    @apply transition-all cursor-pointer;
    transition-duration: 0.3s;
  }

  .interactive:hover {
    transform: translateY(-1px);
  }

  .interactive:active {
    transform: translateY(0);
  }

  .blur-soft {
    backdrop-filter: blur(8px);
  }

  .blur-medium {
    backdrop-filter: blur(12px);
  }

  .blur-strong {
    backdrop-filter: blur(16px);
  }

  .transition-gentle {
    transition: all 0.4s ease;
  }

  .transition-smooth {
    transition: all 0.3s ease;
  }

  .transition-soft {
    transition: all 0.2s ease;
  }

  /* Tło sekcji - jednolite kolory */
  .section-bg {
    background: rgb(var(--color-secondary));
  }

  /* Delikatne animacje */
  .animate-gentle {
    animation: fadeInGentle 1.2s ease forwards;
    opacity: 0;
  }

  @keyframes fadeInGentle {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Opóźnienia dla animacji */
  .delay-50 { animation-delay: 50ms; }
  .delay-150 { animation-delay: 150ms; }
  .delay-250 { animation-delay: 250ms; }
  .delay-350 { animation-delay: 350ms; }
  .delay-450 { animation-delay: 450ms; }



  /* Dodatkowe klasy dla lepszej spójności */
  .text-light {
    font-weight: 300;
  }

  .heading-light {
    font-weight: 300;
    letter-spacing: -0.01em;
  }

  /* Elegancki loader - mindful breathing */
  .mindful-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
  }

  .breath-circle {
    width: 60px;
    height: 60px;
    border: 2px solid rgb(var(--color-sage));
    animation: breathe 4s ease-in-out infinite;
  }

  .breath-text {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: rgb(var(--color-accent));
    font-weight: 300;
    letter-spacing: 0.1em;
  }

  @keyframes breathe {
    0%, 100% {
      transform: scale(1);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.2);
      opacity: 1;
    }
  }

  /* Fallback loader */
  .loader {
    width: 40px;
    height: 40px;
    border: 1px solid rgb(var(--color-sage-light));
    border-top-color: rgb(var(--color-sage));
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Mikro-animacje dla liczb/statystyk */
  .stat-number {
    font-variant-numeric: tabular-nums;
    letter-spacing: -0.02em;
    font-weight: 300;
  }

  @keyframes countUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-count-up {
    animation: countUp 0.8s ease forwards;
  }

  /* Eleganckie powiadomienia/toasty */
  .toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgb(var(--color-primary));
    color: rgb(var(--color-secondary));
    padding: 1rem 2rem;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    font-weight: 300;
    border-radius: 0;
    box-shadow: var(--shadow-medium);
    animation: slideInToast 0.3s ease;
    z-index: 1000;
  }

  @keyframes slideInToast {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .toast.success {
    background: rgb(var(--color-accent));
    color: rgb(var(--color-secondary));
  }

  .toast.error {
    background: #dc2626;
    color: white;
  }

  /* Opcjonalne wsparcie dla dark mode */
  @media (prefers-color-scheme: dark) {
    :root {
      --color-primary: 248 246 243;
      --color-secondary: 26 26 26;
      --color-accent: 139 115 85;
      --color-soft-sage: 60 60 60;
      --color-warm-gold: 212 165 116;
    }
  }

  /* Smooth transitions dla zmiany motywu */
  * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }

  .section-unified-title {
    @apply text-center mb-16;
  }

  .section-unified-title h2 {
    @apply mb-6 text-temple font-light;
  }

  .section-unified-title p {
    @apply max-w-3xl mx-auto text-lg font-light;
    color: rgb(var(--color-wood-light) / 0.85);
  }

  /* Spójne style dla wszystkich kontenerów */
  .container-unified {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-unified-content {
    @apply max-w-5xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Spójne style dla wszystkich grid-ów */
  .grid-unified {
    @apply grid gap-8;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .grid-unified-small {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .grid-unified-large {
    @apply grid gap-10;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .line-clamp-4 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
  }

  /* Resource Library specific styles */
  .resource-card {
    @apply bg-white shadow-soft hover:shadow-medium transition-all duration-300 border border-accent/10;
  }

  .resource-card:hover {
    @apply transform -translate-y-1;
  }

  .resource-preview {
    @apply relative mb-4 overflow-hidden bg-accent/5 h-48;
  }

  .resource-tag {
    @apply text-xs bg-accent/10 text-accent px-3 py-1 border border-accent/20 font-light;
  }

  /* Payment method styles */
  .payment-method-card {
    @apply relative border p-4 cursor-pointer transition-all duration-300;
  }

  .payment-method-card.selected {
    @apply border-temple bg-temple/5 shadow-medium;
  }

  .payment-method-card:not(.selected) {
    @apply border-gray-200 hover:border-temple/50;
  }

  .payment-method-card:not(.selected):hover {
    background-color: rgb(var(--color-temple) / 0.02);
  }

  .payment-method-popular {
    @apply absolute -top-2 left-4 bg-accent/90 text-primary text-xs px-3 py-1 border border-accent/20 font-light;
  }

  /* Waiting list styles */
  .waiting-list-benefit {
    @apply text-sm text-wood-light space-y-1;
  }

  .waiting-list-form {
    @apply space-y-4;
  }

  .waiting-list-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-temple focus:border-transparent;
  }

  /* BLIK specific styles */
  .blik-info {
    @apply bg-accent/5 p-3 border border-accent/10;
  }

  .blik-steps {
    @apply text-sm text-blue-800 space-y-1;
  }

  .blik-step {
    @apply list-decimal ml-4;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 85vh;
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Więcej przestrzeni na mobile - nie zmniejszaj paddingów */
  .section-padding {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .card-content {
    padding: 2rem;
  }

  .btn {
    @apply px-6 py-3 text-sm;
  }

  .nav-link {
    @apply px-4 py-2;
  }

  .hero-title {
    @apply text-3xl md:text-4xl;
  }

  .hero-subtitle {
    @apply text-lg md:text-xl;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in {
    animation-duration: 0.8s;
  }

  /* Ukryj mniej ważne elementy na mobile */
  .mobile-hidden {
    display: none;
  }

  /* Kreatywny stack - niektóre elementy obok siebie */
  .mobile-creative-stack {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }

  /* Section numbers mniejsze na mobile */
  .section-number {
    font-size: clamp(3rem, 8vw, 6rem);
    top: 1rem;
    left: 1rem;
  }

  /* Quote huge mniejszy na mobile */
  .quote-huge {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
  }

  .quote-huge::before {
    font-size: clamp(2rem, 6vw, 4rem);
    top: -1rem;
    left: -1rem;
  }

  /* Gesture hints */
  .gesture-hint {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 0.5rem;
    border-radius: 50%;
    animation: bounce 2s infinite;
    z-index: 40;
  }

  /* Swipe hint dla mobile */
  .swipe-hint {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    animation: swipeUp 2s ease-in-out infinite;
    color: rgb(var(--color-accent) / 0.7);
    font-size: 0.875rem;
    font-weight: 300;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-10px);
    }
    60% {
      transform: translateY(-5px);
    }
  }

  @keyframes swipeUp {
    0%, 100% {
      transform: translateX(-50%) translateY(0);
      opacity: 0.7;
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
      opacity: 1;
    }
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 80vh;
  }

  .section-padding {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .card-content {
    padding: 1rem;
  }

  .btn {
    @apply px-4 py-2 text-xs;
  }
}

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-fade-in,
  .animate-slide-up,
  .animate-scale-in,
  .animate-hover-lift,
  .animate-float {
    animation: none !important;
  }
}

@media print {
  * {
    box-shadow: none !important;
    text-shadow: none !important;
    background: transparent !important;
    color: black !important;
  }
  
  .navbar,
  .btn,
  .hero-section,
  footer {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
    font-size: 12pt;
    line-height: 1.4;
  }

  a,
  a:visited {
    text-decoration: underline;
    color: #444 !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
  }

  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }

  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }

  h2, h3 {
    page-break-after: avoid;
  }

  .card {
    border: 1px solid #ddd !important;
    page-break-inside: avoid;
  }
}

/* React Big Calendar Styles - loaded dynamically in component */

.custom-calendar {
  font-family: var(--font-montserrat);
}

.custom-calendar .rbc-header {
  background-color: rgb(var(--color-primary) / 0.05);
  color: rgb(var(--color-primary));
  font-weight: 500;
  padding: 12px 8px;
  border-bottom: 1px solid rgb(var(--color-primary) / 0.1);
}

.custom-calendar .rbc-today {
  background-color: rgb(var(--color-primary) / 0.05);
}

.custom-calendar .rbc-off-range-bg {
  background-color: rgb(var(--color-primary) / 0.02);
}

.custom-calendar .rbc-event {
  border-radius: 6px;
  border: none;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
}

.custom-calendar .rbc-event:focus {
  outline: 2px solid rgb(var(--color-primary) / 0.5);
}

.custom-calendar .rbc-toolbar {
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 12px;
}

.custom-calendar .rbc-toolbar button {
  background-color: white;
  border: 1px solid rgb(var(--color-primary) / 0.2);
  color: rgb(var(--color-primary));
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.custom-calendar .rbc-toolbar button:hover {
  background-color: rgb(var(--color-primary) / 0.05);
  border-color: rgb(var(--color-primary) / 0.3);
}

.custom-calendar .rbc-toolbar button.rbc-active {
  background-color: rgb(var(--color-primary));
  color: white;
  border-color: rgb(var(--color-primary));
}

.custom-calendar .rbc-month-view {
  border: 1px solid rgb(var(--color-primary) / 0.1);
  border-radius: 12px;
  overflow: hidden;
}

.custom-calendar .rbc-date-cell {
  padding: 8px;
  text-align: center;
}

.custom-calendar .rbc-date-cell > a {
  color: rgb(var(--color-primary));
  font-weight: 500;
}

@media (max-width: 768px) {
  .custom-calendar .rbc-toolbar {
    flex-direction: column;
    align-items: center;
  }

  .custom-calendar .rbc-toolbar-label {
    order: -1;
    margin-bottom: 12px;
    font-size: 18px;
    font-weight: 600;
  }
}
