(()=>{var e={};e.id=3831,e.ids=[3831],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13982:(e,t,r)=>{Promise.resolve().then(r.bind(r,97379))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35585:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,metadata:()=>o});var s=r(37413),a=r(61120),l=r(66945),i=r(66393);let o={title:"Blog - Joga, Podr\xf3że i Inspiracje z Bali",description:"Najnowsze artykuły o jodze na Bali, slow travel, medytacji i odkrywaniu piękna Wyspy Bog\xf3w. Porady, inspiracje i relacje z podr\xf3ży.",keywords:["blog joga bali","artykuły joga","slow travel bali","medytacja","inspiracje podr\xf3żnicze bali"],alternates:{canonical:"/blog"},openGraph:{title:"Blog - Joga, Podr\xf3że i Inspiracje z Bali",description:"Odkryj nasz blog o jodze i podr\xf3żach na Bali.",images:[{url:"/og-image-blog.jpg",width:1200,height:630,alt:"Blog Bali Yoga Journey"}]}};function n(){let e=(0,l.zX)();return e&&Array.isArray(e)?(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:"Ładowanie artykuł\xf3w..."}),children:(0,s.jsx)(i.default,{posts:e})}):(0,s.jsx)("div",{children:"Brak post\xf3w do wyświetlenia."})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66393:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\blog_prod\\\\my-travel-blog\\\\src\\\\app\\\\blog\\\\BlogPageClientContent.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\blog\\BlogPageClientContent.jsx","default")},66418:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),o=r(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d={children:["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35585)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\blog\\page.jsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(r.bind(r,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\blog\\page.jsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90422:(e,t,r)=>{Promise.resolve().then(r.bind(r,66393))},97379:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687),a=r(85814),l=r.n(a),i=r(30474),o=r(43210),n=r(52792);let d=({iconName:e,className:t=""})=>(0,s.jsx)(n.Ay,{name:e,className:t}),c=({post:e,featured:t=!1})=>{let r=(0,o.useMemo)(()=>{if(!e?.date)return"Data nieznana";try{return new Date(e.date).toLocaleDateString("pl-PL",{year:"numeric",month:"long",day:"numeric"})}catch(e){return"Data nieznana"}},[e?.date]),a=(0,o.useMemo)(()=>Array.isArray(e?.tags)?e.tags.slice(0,2):[],[e?.tags]),n=(0,o.useMemo)(()=>Array.isArray(e?.tags)?Math.max(0,e.tags.length-2):0,[e?.tags]);return e?(0,s.jsx)("article",{className:`group relative overflow-hidden transition-all duration-500 hover:-translate-y-2 ${t?"unified-card bg-gradient-to-br from-shell/95 to-rice/90 border-2 border-golden/20 shadow-warm hover:shadow-xl":"unified-card"}`,children:(0,s.jsxs)(l(),{href:`/blog/${e.slug||"#"}`,className:"block h-full",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden rounded-t-lg",children:[(0,s.jsx)("div",{className:"relative w-full h-[240px]",children:(0,s.jsx)(i.default,{src:e.imageUrl||"/images/placeholder/image.jpg",alt:e.imageAlt||e.title||"Blog image",fill:!0,className:"object-cover transition-all duration-700 group-hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",quality:85,priority:t})}),(0,s.jsx)("div",{className:"absolute top-3 left-3",children:(0,s.jsx)("span",{className:"px-3 py-1.5 bg-temple/85 text-rice text-xs font-medium uppercase tracking-wide backdrop-blur-sm rounded-full border border-rice/20",children:e.category||"Joga"})})]}),(0,s.jsxs)("div",{className:"p-6 flex flex-col flex-grow",children:[(0,s.jsx)("h2",{className:`font-serif leading-tight mb-3 group-hover:text-golden transition-colors duration-300 ${t?"text-2xl font-bold text-temple":"text-xl font-semibold text-temple"}`,children:e.title||"Bez tytułu"}),(0,s.jsx)("p",{className:"text-wood-light text-sm leading-relaxed mb-4 flex-grow line-clamp-3 font-light",children:e.excerpt||""}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-xs text-wood-light/70",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(d,{iconName:"Calendar",className:"w-3.5 h-3.5 text-temple/60"}),(0,s.jsx)("span",{className:"font-medium",children:r})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsx)(d,{iconName:"User",className:"w-3.5 h-3.5 text-temple/60"}),(0,s.jsx)("span",{className:"font-medium",children:e.author||"Julia"})]})]}),a.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1.5",children:[a.map((e,t)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-temple/5 text-temple text-xs font-medium rounded-full border border-temple/10",children:e},`tag-${t}-${e}`)),n>0&&(0,s.jsxs)("span",{className:"px-2 py-1 text-wood-light/50 text-xs font-medium",children:["+",n]})]})]}),(0,s.jsx)("div",{className:"mt-4 pt-3 border-t border-temple/10",children:(0,s.jsxs)("div",{className:"flex items-center justify-between group/btn",children:[(0,s.jsx)("span",{className:"text-temple font-medium text-sm group-hover:text-golden transition-colors",children:"Czytaj więcej"}),(0,s.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full bg-temple/5 group-hover:bg-golden/10 transition-all duration-300",children:(0,s.jsx)(d,{iconName:"ArrowRight",className:"w-4 h-4 text-temple group-hover:text-golden group-hover:translate-x-0.5 transition-all duration-300"})})]})})]})]})}):null};function m({posts:e=[]}){let t=(0,o.useMemo)(()=>Array.isArray(e)?e.filter(e=>e&&"object"==typeof e):[],[e]);return(0,o.useMemo)(()=>t.filter(e=>e?.featured===!0),[t]),(0,o.useMemo)(()=>t.filter(e=>e?.featured!==!0),[t]),(0,s.jsxs)("div",{className:"relative section-bg min-h-screen",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-temple/5 via-transparent to-golden/5"}),(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h1",{className:"text-5xl md:text-6xl font-serif text-temple tracking-tight mb-6 leading-tight font-light",children:["Inspiracje z ",(0,s.jsx)("span",{className:"text-golden",children:"Bali"})]}),(0,s.jsx)("div",{className:"decorative-line"}),(0,s.jsx)("p",{className:"text-lg text-wood-light/90 max-w-2xl mx-auto leading-relaxed font-light",children:"Odkryj transformacyjną moc jogi, poznaj sekrety Wyspy Bog\xf3w i zanurz się w świecie mindfulness i duchowego rozwoju."})]})})]}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pb-20",children:[(0,s.jsx)("section",{className:"mb-20",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.length>0?t.map((e,t)=>(0,s.jsx)(c,{post:e},`post-${e?.slug||t}`)):(0,s.jsx)("div",{className:"col-span-full text-center py-16",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsx)(d,{iconName:"Eye",className:"w-12 h-12 text-temple/30 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-serif text-temple mb-2",children:"Wkr\xf3tce więcej treści"}),(0,s.jsx)("p",{className:"text-wood-light/70 text-sm",children:"Pracujemy nad nowymi inspirującymi artykułami"})]})})})}),(0,s.jsx)("section",{className:"mt-24",children:(0,s.jsx)("div",{className:"unified-card bg-gradient-to-br from-shell/60 via-rice/80 to-mist/60 p-12 text-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 px-4 py-2 bg-temple/5 rounded-full text-temple text-sm font-medium mb-6 backdrop-blur-sm border border-temple/10",children:[(0,s.jsx)(d,{iconName:"Heart",className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Dołącz do społeczności"})]}),(0,s.jsx)("h3",{className:"text-3xl font-serif text-temple mb-4 font-light",children:"Bądź na bieżąco"}),(0,s.jsx)("p",{className:"text-wood-light text-lg mb-8 max-w-2xl mx-auto font-light",children:"Otrzymuj najnowsze artykuły, inspiracje z Bali i ekskluzywne treści prosto na sw\xf3j email"})]})})})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>r(66418));module.exports=s})();