"use strict";(()=>{var e={};e.id=1543,e.ids=[1543],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},45069:(e,r,o)=>{o.r(r),o.d(r,{default:()=>t});var s=o(37413);function t({params:e}){return(0,s.jsx)("div",{children:(0,s.jsxs)("h1",{children:["<PERSON> wycieczki: ",e.slug]})})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80314:(e,r,o)=>{o.r(r),o.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>g,tree:()=>p});var s=o(65239),t=o(48088),a=o(88170),n=o.n(a),d=o(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);o.d(r,i);let p={children:["",{children:["program",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,45069)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\program\\[slug]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,2283)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\layout.jsx"],error:[()=>Promise.resolve().then(o.bind(o,42201)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\error.jsx"],"not-found":[()=>Promise.resolve().then(o.bind(o,43867)),"C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\not-found.jsx"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\Projekty\\blog_prod\\my-travel-blog\\src\\app\\program\\[slug]\\page.jsx"],u={require:o,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/program/[slug]/page",pathname:"/program/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})}};var r=require("../../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),s=r.X(0,[3434,596,6027,6979,1097,772,8391,1199,3607,460,4165,7283,8046,4133,7684,7824,4858,2771,8909,2076],()=>o(80314));module.exports=s})();