'use client';

import React from 'react';

export default function WhatsAppButton() {
  const phoneNumber = '48606101523';
  const message = 'Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?';
  const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

  return (
    <a
      href={whatsappUrl}
      target="_blank"
      rel="noopener noreferrer"
      className="fixed bottom-8 right-8 text-primary px-6 py-4 font-body font-normal text-sm tracking-[1px] transition-all duration-300 hover:-translate-y-1 z-50"
      aria-label="Skontaktuj się przez WhatsApp"
      style={{
        backgroundColor: '#7C9885',
        boxShadow: '0 5px 25px rgba(124,152,133,0.3)'
      }}
    >
      NAPISZ DO NAS
    </a>
  );
}
